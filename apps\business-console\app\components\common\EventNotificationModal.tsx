// app/components/common/EventNotificationModal.tsx
import React, { useState, useEffect, useRef } from "react";
import { useFetcher } from "@remix-run/react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Loader2, Send, AlertCircle } from "lucide-react";
import { useToast } from "~/hooks/use-toast";
import {
  getAvailableEventTypes,
  getEventConfig,
  validateEventPayload,
  type EventType,
  type EventPayload,
  type SendEventNotificationRequest,
  type EventField,
  type EventConfig,
} from "~/services/eventNotificationService.client";
import { rNETOrder } from "~/types/api/rNETOrders";

// ============================================================================
// INTERFACES
// ============================================================================

interface EventNotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: rNETOrder | null;
}

interface FormData {
  eventType: EventType | "";
  [key: string]: string;
}

interface FormFieldProps {
  field: EventField;
  value: string;
  error?: string;
  onChange: (name: string, value: string) => void;
}

interface OrderInfoProps {
  order: rNETOrder;
}

interface EventSelectorProps {
  availableEvents: EventConfig[];
  selectedEventType: EventType | "";
  onEventTypeChange: (eventType: string) => void;
}

interface EventFormProps {
  eventConfig: EventConfig;
  formData: Record<string, string>;
  validationErrors: Record<string, string>;
  onFieldChange: (name: string, value: string) => void;
}

interface ErrorAlertProps {
  errors: string[];
}

// ============================================================================
// SUB-COMPONENTS
// ============================================================================

/**
 * Reusable form field component that handles different input types
 */
function FormField({ field, value, error, onChange }: FormFieldProps) {
  const commonProps = {
    id: field.name,
    name: field.name,
    value,
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      onChange(field.name, e.target.value),
    placeholder: field.placeholder,
    required: field.required,
    className: error ? "border-red-500" : "",
  };

  const renderFieldInput = () => {
    switch (field.type) {
      case "textarea":
        return (
          <Textarea
            {...commonProps}
            rows={3}
          />
        );

      case "select":
        return (
          <Select
            value={value}
            onValueChange={(selectedValue) => onChange(field.name, selectedValue)}
            required={field.required}
          >
            <SelectTrigger className={error ? "border-red-500" : ""}>
              <SelectValue placeholder={field.placeholder || `Select ${field.label}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      default:
        return (
          <Input
            {...commonProps}
            type="text"
          />
        );
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={field.name} className="text-sm font-medium">
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {renderFieldInput()}
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  );
}

/**
 * Component to display order information
 */
function OrderInfo({ order }: OrderInfoProps) {
  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <h3 className="font-medium text-sm text-gray-700 mb-2">Order Information</h3>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-gray-600">Order ID:</span>
          <span className="ml-2 font-medium">#{order.orderGroupId}</span>
        </div>
        <div>
          <span className="text-gray-600">Customer:</span>
          <span className="ml-2 font-medium">{order.customerName || "N/A"}</span>
        </div>
        <div>
          <span className="text-gray-600">Mobile:</span>
          <span className="ml-2 font-medium">{order.customerMobileNumber || "N/A"}</span>
        </div>
        <div>
          <span className="text-gray-600">Status:</span>
          <span className="ml-2 font-medium">{order.orderStatus}</span>
        </div>
      </div>
    </div>
  );
}

/**
 * Component for selecting event type
 */
function EventSelector({ 
  availableEvents, 
  selectedEventType, 
  onEventTypeChange 
}: EventSelectorProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor="eventType" className="text-sm font-medium">
        Select Event
        <span className="text-red-500 ml-1">*</span>
      </Label>
      <Select
        value={selectedEventType}
        onValueChange={onEventTypeChange}
        required
      >
        <SelectTrigger>
          <SelectValue placeholder="Choose an event type" />
        </SelectTrigger>
        <SelectContent>
          {availableEvents.map((event) => (
            <SelectItem key={event.type} value={event.type}>
              <div>
                <div className="font-medium">{event.label}</div>
                <div className="text-xs text-gray-500">{event.description}</div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

/**
 * Component for rendering dynamic event form fields
 */
function EventForm({ 
  eventConfig, 
  formData, 
  validationErrors, 
  onFieldChange 
}: EventFormProps) {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-medium text-sm text-gray-700 mb-3">Event Data</h3>
        <div className="space-y-4">
          {eventConfig.fields.map((field) => (
            <FormField
              key={field.name}
              field={field}
              value={formData[field.name] || ""}
              error={validationErrors[field.name]}
              onChange={onFieldChange}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * Component for displaying error messages
 */
function ErrorAlert({ errors }: ErrorAlertProps) {
  if (errors.length === 0) {
    return null;
  }

  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        <ul className="list-disc list-inside space-y-1">
          {errors.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}

// ============================================================================
// CUSTOM HOOK
// ============================================================================

/**
 * Custom hook to manage event notification state and logic
 */
function useEventNotification({ isOpen, order, onClose }: EventNotificationModalProps) {
  const [formData, setFormData] = useState<FormData>({ eventType: "" });
  const [errors, setErrors] = useState<string[]>([]);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [fetcherKey, setFetcherKey] = useState(0);
  const fetcher = useFetcher({ key: `event-notification-${fetcherKey}` });
  const responseProcessedRef = useRef(false);
  const { toast } = useToast();

  const isSubmitting = fetcher.state === "submitting";
  const selectedEventConfig = formData.eventType ? getEventConfig(formData.eventType) : null;

  // Reset form when modal opens/closes or order changes
  useEffect(() => {
    if (isOpen && order) {
      setFormData({ eventType: "" });
      setErrors([]);
      setValidationErrors({});
      responseProcessedRef.current = false;
      
      // Create a new fetcher instance to clear stale data
      setFetcherKey(prev => prev + 1);
    }
  }, [isOpen, order]);

  // Reset form when event type changes
  useEffect(() => {
    if (formData.eventType) {
      const newFormData: FormData = { eventType: formData.eventType };
      setFormData(newFormData);
      setValidationErrors({});
    }
  }, [formData.eventType]);

  // Handle fetcher response
  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle" && !responseProcessedRef.current) {
      responseProcessedRef.current = true;
      
      const response = fetcher.data as { success?: boolean; errorMessage?: string };
      
      if (response.success) {
        // Show success toast
        toast({
          title: "Success",
          description: "Event notification sent successfully!",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        });
        
        // Close modal after a short delay
        setTimeout(() => {
          handleClose();
        }, 1000);
      } else {
        const errorMessage = response.errorMessage || "Failed to send notification";
        setErrors([errorMessage]);
        
        // Show error toast
        toast({
          title: "Error",
          description: errorMessage,
          style: {
            backgroundColor: "#dc2626",
            color: "#ffffff"
          }
        });
      }
    }
  }, [fetcher.data, fetcher.state, toast]);

  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    if (!formData.eventType) {
      setErrors(["Please select an event type"]);
      return false;
    }

    if (!order) {
      setErrors(["No order selected"]);
      return false;
    }

    // Validate event-specific fields
    const payload: Partial<EventPayload> = {
      orderGroupId: order.orderGroupId,
    };

    // Add event-specific fields to payload
    selectedEventConfig?.fields.forEach(field => {
      if (formData[field.name]) {
        (payload as Record<string, unknown>)[field.name] = formData[field.name];
      }
    });

    const validation = validateEventPayload(formData.eventType, payload);
    
    if (!validation.isValid) {
      setErrors(validation.errors);
      return false;
    }

    setErrors([]);
    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !order || !formData.eventType) {
      return;
    }

    setErrors([]);
    responseProcessedRef.current = false;

    // Build payload based on event type
    const payload: EventPayload = {
      orderGroupId: order.orderGroupId,
    } as EventPayload;

    // Add event-specific fields
    selectedEventConfig?.fields.forEach(field => {
      if (formData[field.name]) {
        (payload as Record<string, unknown>)[field.name] = formData[field.name];
      }
    });

    const eventData: SendEventNotificationRequest = {
      eventType: formData.eventType,
      customerMobileNumber: order.customerMobileNumber || null,
      customerName: order.customerName || null,
      sellerId: order.sellerId || null,
      payload,
    };

    // Submit using fetcher
    const formDataToSubmit = new FormData();
    formDataToSubmit.append("intent", "Send Event Notification");
    formDataToSubmit.append("data", JSON.stringify({ eventData }));
    
    console.log("Submitting event notification:", eventData);
    fetcher.submit(formDataToSubmit, { method: "post" });
  };

  const handleClose = () => {
    if (!isSubmitting) {
      // Reset state before closing
      setFormData({ eventType: "" });
      setErrors([]);
      setValidationErrors({});
      responseProcessedRef.current = false;
      onClose();
    }
  };

  return {
    formData,
    errors,
    validationErrors,
    isSubmitting,
    selectedEventConfig,
    handleInputChange,
    handleSubmit,
    handleClose,
  };
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * EventNotificationModal - Modal for sending event notifications to customers
 */
export function EventNotificationModal({
  isOpen,
  onClose,
  order,
}: EventNotificationModalProps) {
  const {
    formData,
    errors,
    validationErrors,
    isSubmitting,
    selectedEventConfig,
    handleInputChange,
    handleSubmit,
    handleClose,
  } = useEventNotification({ isOpen, order, onClose });

  const availableEvents = getAvailableEventTypes?.() || [];

  if (!order) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold flex items-center gap-2">
            <Send className="w-5 h-5" />
            Send Event Notification
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Information */}
          <OrderInfo order={order} />

          {/* Event Selection */}
          <EventSelector
            availableEvents={availableEvents}
            selectedEventType={formData.eventType}
            onEventTypeChange={(value) => handleInputChange("eventType", value)}
          />

          {/* Event Data Form */}
          {selectedEventConfig && (
            <EventForm
              eventConfig={selectedEventConfig}
              formData={formData}
              validationErrors={validationErrors}
              onFieldChange={handleInputChange}
            />
          )}

          {/* Error Messages */}
          <ErrorAlert errors={errors} />
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isSubmitting || !formData.eventType}
            variant="default"
            loading={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="w-4 h-4" />
                Send Message
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
