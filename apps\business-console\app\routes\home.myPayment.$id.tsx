import { LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { useState } from "react";
import { getPayoutDetails } from "~/services/payments";
import type { DistributorItemBD, OrderGroupBD, PayoutDetails, SupplierItemBD } from "~/types/api/businessConsoleService/Payouts";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Badge } from "~/components/ui/badge";
import ResponsivePagination from "~/components/ui/responsivePagination";
import { Button } from "~/components/ui/button";
import { ArrowLeft, ChevronDown, ChevronRight } from "lucide-react";
import { calculateTotalKeySum } from "~/utils/format";
import ShowDecimalAsSubscript from "~/components/common/ShowDecimalAsSubscript";

interface LoaderData {
  payoutDetails: PayoutDetails;
}

export const loader: LoaderFunction = withAuth(async ({ request, params }) => {
  const payoutId = params?.id;
  if (!payoutId) {
    throw new Response("Invalid payout ID", { status: 400 });
  }

  try {
    const response = await getPayoutDetails(Number(payoutId), request);
    return withResponse({
      payoutDetails: response.data
    }, response?.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to get payout details", { status: 500 });
  }
});

// Utility functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

const getStatusBadge = (status: string) => {
  const statusLower = status.toLowerCase();
  if (statusLower === "open") {
    return <Badge variant="default" className="bg-green-100 hover:bg-green-100 text-green-800 text-xs sm:text-sm">Open</Badge>;
  } else if (statusLower === "billingclosed") {
    return <Badge variant="secondary" className="bg-yellow-100 hover:bg-yellow-100 text-yellow-800 text-xs sm:text-sm whitespace-nowrap">Billing Closed</Badge>;
  } else if (statusLower === "paid") {
    return <Badge variant="outline" className="text-xs sm:text-sm">Paid</Badge>;
  }
  return <Badge variant="outline" className="text-xs sm:text-sm whitespace-nowrap">{status}</Badge>;
};

// Collapsible Section Component
interface CollapsibleSectionProps {
  title: string;
  count: number;
  isExpanded: boolean;
  onToggle: () => void;
  summaryContent: React.ReactNode;
  children: React.ReactNode;
  downloadAction?: () => void;
}

const CollapsibleSection = ({
  title,
  count,
  isExpanded,
  onToggle,
  summaryContent,
  children,
}: CollapsibleSectionProps) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between cursor-pointer" onClick={onToggle}>
        <div className="flex items-center space-x-2">
          {isExpanded ? (
            <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
          ) : (
            <ChevronRight className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
          )}
          <CardTitle className="text-base sm:text-xl">
            {title} ({count})
          </CardTitle>
        </div>
        <div>
          {summaryContent}
        </div>
      </CardHeader>

      {isExpanded ? (
        <CardContent className="p-3 pt-0">
          {children}
        </CardContent>
      ) : null}
    </Card>
  );
};

// Pagination hook
const usePagination = (data: any[], itemsPerPage: number = 10) => {
  const [currentPage, setCurrentPage] = useState(0);
  const totalPages = Math.ceil(data.length / itemsPerPage);

  const paginatedData = data.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  return {
    currentPage,
    totalPages,
    paginatedData,
    setCurrentPage
  };
};

// Table components
const SupplierItemsTable = ({ data, totals }: { data: SupplierItemBD[], totals: Partial<SupplierItemBD> }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-2">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item</TableHead>
              <TableHead>Distributor</TableHead>
              {totals.totalWeight ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap">Weight</div>
                  <div className="whitespace-nowrap">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.totalWeight)} decFontSize="11px" />{" kg"})</div>
                </div>
              </TableHead> : null}
              {totals.itemsAmount ? <TableHead className="whitespace-nowrap text-right">Price (₹/unit)</TableHead> : null}
              {totals.itemsAmount ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Items Amount (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.itemsAmount)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
              {totals.pc ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Platform Comm (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.pc)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
              {totals.pcTax ? <TableHead className="whitespace-nowrap text-right">PC Tax (₹)</TableHead> : null}
              {totals.sc ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Sales Comm (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.sc)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
              {totals.scTax ? <TableHead className="whitespace-nowrap text-right">SC Tax (₹)</TableHead> : null}
              {totals.dhc ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Dist Charge (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.dhc)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
              {totals.dhcTax ? <TableHead className="whitespace-nowrap text-right">DHC Tax (₹)</TableHead> : null}
              {/* {totals.itemsStrikeoffAmount ? <TableHead className="whitespace-nowrap text-right">Strikeoff Amount (₹)</TableHead> : null} */}
              {/* {totals.itemsDiscount ? <TableHead className="whitespace-nowrap text-right">Items Discount (₹)</TableHead> : null} */}
              {totals.netAmount ? <TableHead className="bg-muted/80">
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Net Amount (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.netAmount)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sellerItemName}</TableCell>
                <TableCell>{item.distributorName}</TableCell>
                {totals.totalWeight ? <TableCell className="whitespace-nowrap">{item.totalWeight} {item.unit}</TableCell> : null}
                {totals.itemsAmount ? <TableCell className="whitespace-nowrap" align="right">{item.itemsAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.itemsAmount / item.totalWeight)} decFontSize="11px" />}</TableCell> : null}
                {totals.itemsAmount ? <TableCell className="whitespace-nowrap" align="right">{item.itemsAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.itemsAmount)} decFontSize="11px" />}</TableCell> : null}
                {totals.pc ? <TableCell className="whitespace-nowrap" align="right">{item.pc === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.pc)} decFontSize="11px" />}</TableCell> : null}
                {totals.pcTax ? <TableCell className="whitespace-nowrap" align="right">{item.pcTax === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.pcTax)} decFontSize="11px" />}</TableCell> : null}
                {totals.sc ? <TableCell className="whitespace-nowrap" align="right">{item.sc === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.sc)} decFontSize="11px" />}</TableCell> : null}
                {totals.scTax ? <TableCell className="whitespace-nowrap" align="right">{item.scTax === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.scTax)} decFontSize="11px" />}</TableCell> : null}
                {totals.dhc ? <TableCell className="whitespace-nowrap" align="right">{item.dhc === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.dhc)} decFontSize="11px" />}</TableCell> : null}
                {totals.dhcTax ? <TableCell className="whitespace-nowrap" align="right">{item.dhcTax === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.dhcTax)} decFontSize="11px" />}</TableCell> : null}
                {/* {totals.itemsStrikeoffAmount ? <TableCell className="whitespace-nowrap" align="right">{item.itemsStrikeoffAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.itemsStrikeoffAmount)} decFontSize="11px" />}</TableCell> : null} */}
                {/* {totals.itemsDiscount ? <TableCell className="whitespace-nowrap" align="right">{item.itemsDiscount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.itemsDiscount)} decFontSize="11px" />}</TableCell> : null} */}
                {totals.netAmount ? <TableCell className="whitespace-nowrap bg-muted/80" align="right">{item.netAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.netAmount)} decFontSize="11px" />}</TableCell> : null}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

const DistributorItemsTable = ({ data, totals }: { data: DistributorItemBD[], totals: Partial<DistributorItemBD> }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-2">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item</TableHead>
              <TableHead>Supplier</TableHead>
              {totals.totalWeight ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap">Weight</div>
                  <div className="whitespace-nowrap">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.totalWeight)} decFontSize="11px" />{" kg"})</div>
                </div>
              </TableHead> : null}
              {totals.dhc ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Dist Charge (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.dhc)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
              {totals.dhcTax ? <TableHead className="whitespace-nowrap text-right">DHC Tax (₹)</TableHead> : null}
              {/* {totals.itemsStrikeoffAmount ? <TableHead className="whitespace-nowrap text-right">Strikeoff Amount (₹)</TableHead> : null} */}
              {/* {totals.itemsDiscount ? <TableHead className="whitespace-nowrap text-right">Item Discount (₹)</TableHead> : null} */}
              {totals.netAmount ? <TableHead className="bg-muted/80">
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Net Amount (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.netAmount)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sellerItemName}</TableCell>
                <TableCell>{item.supplierName}</TableCell>
                {totals.totalWeight ? <TableCell className="whitespace-nowrap">{item.totalWeight} {item.unit}</TableCell> : null}
                {totals.dhc ? <TableCell className="whitespace-nowrap" align="right">{item.dhc === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.dhc)} decFontSize="11px" />}</TableCell> : null}
                {totals.dhcTax ? <TableCell className="whitespace-nowrap" align="right">{item.dhcTax === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.dhcTax)} decFontSize="11px" />}</TableCell> : null}
                {/* {totals.itemsStrikeoffAmount ? <TableCell className="whitespace-nowrap" align="right">{item.itemsStrikeoffAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.itemsStrikeoffAmount)} decFontSize="11px" />}</TableCell> : null} */}
                {/* {totals.itemsDiscount ? <TableCell className="whitespace-nowrap" align="right">{item.itemsDiscount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.itemsDiscount)} decFontSize="11px" />}</TableCell> : null} */}
                {totals.netAmount ? <TableCell className="whitespace-nowrap bg-muted/80" align="right">{item.netAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.netAmount)} decFontSize="11px" />}</TableCell> : null}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

const OrderGroupsTable = ({ data, totals }: { data: OrderGroupBD[], totals: Partial<OrderGroupBD> }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-2">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="whitespace-nowrap">Order ID</TableHead>
              <TableHead>Buyer</TableHead>
              <TableHead>Delivery Date</TableHead>
              <TableHead>Driver</TableHead>
              {totals.codAmount ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">COD Amount (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.codAmount)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
              {totals.creditAmount ? <TableHead>
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Credit Amount (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.creditAmount)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
              {totals.netAmount ? <TableHead className="bg-muted/80">
                <div className="flex flex-col text-sm">
                  <div className="whitespace-nowrap text-right">Net Amount (₹)</div>
                  <div className="whitespace-nowrap text-right">(T: <ShowDecimalAsSubscript value={formatCurrency(totals.netAmount)} decFontSize="11px" />)</div>
                </div>
              </TableHead> : null}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.ogId}</TableCell>
                <TableCell>{item.businessName}</TableCell>
                <TableCell className="whitespace-nowrap">{formatDate(item.deliveryDate)}</TableCell>
                <TableCell>{item.driverName}</TableCell>
                {totals.codAmount ? <TableCell align="right">{item.codAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.codAmount)} decFontSize="11px" />}</TableCell> : null}
                {totals.creditAmount ? <TableCell align="right">{item.creditAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.creditAmount)} decFontSize="11px" />}</TableCell> : null}
                {totals.netAmount ? <TableCell align="right" className="bg-muted/80">{item.netAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(item.netAmount)} decFontSize="11px" />}</TableCell> : null}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

export default function PayoutDetails() {
  const { payoutDetails } = useLoaderData<LoaderData>();
  const navigate = useNavigate();

  const [expandedSections, setExpandedSections] = useState({
    supplierItems: false,
    distributorItems: false,
    orderGroups: false
  });

  const supplierTotalSum = calculateTotalKeySum(payoutDetails.supplierItemBds)
  const distributorTotalSum = calculateTotalKeySum(payoutDetails.distributorItemBds)
  const orderGroupTotalSum = calculateTotalKeySum(payoutDetails.orderGroupBds)

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  return (
    <div className="container mx-auto p-4 space-y-4">
      {/* Page Header */}
      <div>
        <Button variant="secondary" size="sm" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
        <div className="flex items-center justify-between sm:px-3 mt-3">
          <h1 className="text-2xl font-bold">Payout Details</h1>
          <Badge variant="outline" className="text-base px-3 py-1">
            ID: {payoutDetails?.sellerPayout?.payoutId}
          </Badge>
        </div>
      </div>

      {/* Seller Payout Details */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Date Range</p>
              <p className="text-lg font-semibold">{payoutDetails?.sellerPayout?.dateRange}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Payment Date</p>
              <p className="text-lg font-semibold">{formatDate(payoutDetails?.sellerPayout?.paymentDate)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              <div>{getStatusBadge(payoutDetails?.sellerPayout?.status)}</div>
            </div>
            <div className="space-y-1 ml-auto">
              <p className="text-sm font-medium text-muted-foreground">Amount (₹)</p>
              <p className="text-lg font-semibold text-blue-600"><ShowDecimalAsSubscript value={formatCurrency(payoutDetails?.sellerPayout?.amount)} decFontSize="0.8125rem" /></p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Supplier Items Section */}
      {payoutDetails?.supplierItemBds?.length > 0 && (
        <CollapsibleSection
          title="My Supplies"
          count={payoutDetails?.supplierItemBds?.length}
          isExpanded={expandedSections.supplierItems}
          onToggle={() => toggleSection('supplierItems')}
          summaryContent={
            <div>
              <p className="sm:text-lg font-semibold"><ShowDecimalAsSubscript value={formatCurrency(supplierTotalSum.netAmount || 0)} decFontSize="0.8125rem" /></p>
            </div>
          }
        >
          <SupplierItemsTable data={payoutDetails?.supplierItemBds} totals={supplierTotalSum} />
        </CollapsibleSection>
      )}

      {/* Distributor Items Section */}
      {payoutDetails?.distributorItemBds?.length > 0 && (
        <CollapsibleSection
          title="Handling Revenue"
          count={payoutDetails?.distributorItemBds?.length}
          isExpanded={expandedSections.distributorItems}
          onToggle={() => toggleSection('distributorItems')}
          summaryContent={
            <div>
              <p className="sm:text-lg font-semibold"><ShowDecimalAsSubscript value={formatCurrency(distributorTotalSum.netAmount || 0)} decFontSize="0.8125rem" /></p>
            </div>
          }
        >
          <DistributorItemsTable data={payoutDetails?.distributorItemBds} totals={distributorTotalSum} />
        </CollapsibleSection>
      )}

      {/* Order Groups Section */}
      {payoutDetails?.orderGroupBds?.length > 0 && (
        <CollapsibleSection
          title="Cod & Credit"
          count={payoutDetails?.orderGroupBds?.length}
          isExpanded={expandedSections.orderGroups}
          onToggle={() => toggleSection('orderGroups')}
          summaryContent={
            <div>
              <p className="sm:text-lg font-semibold"><ShowDecimalAsSubscript value={formatCurrency(orderGroupTotalSum.netAmount || 0)} decFontSize="0.8125rem" /></p>
            </div>
          }
        >
          <OrderGroupsTable data={payoutDetails?.orderGroupBds} totals={orderGroupTotalSum} />
        </CollapsibleSection>
      )}
    </div>
  );
};