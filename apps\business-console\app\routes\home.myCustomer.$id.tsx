import { ActionFunction, LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate, useLocation, useFetcher } from "@remix-run/react";

import { Card, CardContent } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Button } from "~/components/ui/button";
import { ArrowLeft, User } from "lucide-react";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { useEffect, useState } from "react";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogOverlay, DialogTitle } from "~/components/ui/dialog";
import { useToast } from "~/components/ui/ToastProvider";
import ShowDecimalAsSubscript from "~/components/common/ShowDecimalAsSubscript";
import { CustomerCreditSummary, CustomerPendingOrder, getCustomerPendingOrders, recieveBuyerPendingCash } from "~/services/myCustomers";
import { Input } from "~/components/ui/input";

interface LoaderData {
  customerPendingOrders: CustomerPendingOrder[];
  customerId: number;
  error?: string;
}

type ActionIntent = "Recieve Cash";

interface ActionData {
  intent: ActionIntent;
  success: boolean;
  error?: string;
}

export const loader: LoaderFunction = withAuth(async ({ request, params }) => {
  const customerId = params?.id;

  try {
    const response = await getCustomerPendingOrders(Number(customerId), request);
    return withResponse({
      customerPendingOrders: response.data,
      customerId: Number(customerId)
    }, response.headers);
  } catch (error) {
    return withResponse({
      customerPendingOrders: [],
      customerId: Number(customerId),
      error: "Failed to load pending orders"
    }, new Headers());
  }
});

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("intent");

  if (intent === "Recieve Cash") {
    try {
      const { amount, nBuyerId, note } = JSON.parse(formData.get("data") as string);
      await recieveBuyerPendingCash(nBuyerId, amount, note, request);
      return { success: true };
    } catch (error) {
      return { success: false, error: "Failed to recieve cash" };
    }
  }

  return { success: false, error: "Invalid intent" };
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

export default function MyCustomer() {
  const { customerPendingOrders, customerId, error } = useLoaderData<LoaderData>();
  const navigate = useNavigate();
  const location = useLocation();

  // action
  const fetcher = useFetcher<ActionData>();
  const isSubmitting = fetcher.state === "submitting" || fetcher.state === "loading";
  const { showToast } = useToast();

  const [openModalFrom, setOpenModalFrom] = useState(false);
  const [paymentType, setPaymentType] = useState<'full' | 'partial'>('full');
  const [selectedAmount, setSelectedAmount] = useState(0);

  // Get customer details from location state (passed from the customers list)
  const customerDetails = location.state?.customerDetails as CustomerCreditSummary | undefined;

  // summary 
  const totalCreditPendingAmount = customerPendingOrders.reduce((sum, customer) => sum + customer.creditPendingAmount, 0);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("intent", "Recieve Cash");
    formData.append("data", JSON.stringify({
      amount: selectedAmount,
      nBuyerId: customerId,
      note: ''
    }));
    fetcher.submit(formData, { method: "post" });
    setOpenModalFrom(false);
  }

  useEffect(() => {
    if (fetcher.data?.success) {
      showToast("Cash collected successfully", "success");
      setOpenModalFrom(false);
    } else if (fetcher.data?.error) {
      showToast(fetcher.data.error, "error");
    }
  }, [fetcher.data]);

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-7xl">
      {isSubmitting && (
        <div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 pointer-events-none z-50 p-4"
        >
          <div className="p-6 text-center">
            <div className="w-20 h-20 border-4 border-transparent border-t-blue-400 rounded-full animate-spin flex items-center justify-center">
              <div className="w-16 h-16 border-4 border-transparent border-t-red-400 rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {/* Page Header */}
        <div>
          <Button variant="secondary" size="sm" onClick={() => navigate(-1)} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Customers
          </Button>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-lg sm:text-2xl font-bold text-gray-900 break-words">
                {customerDetails?.buyerName}
              </h1>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-start sm:items-center sm:justify-end">
              <Button
                className="w-full sm:w-auto px-6 sm:px-8 sm:mr-4"
                onClick={() => {
                  setOpenModalFrom(true)
                  setPaymentType('full')
                  setSelectedAmount(totalCreditPendingAmount)
                }}
                disabled={totalCreditPendingAmount <= 0}
              >
                Collect
              </Button>
            </div>
          </div>
        </div>

        {error ? (<div className="p-6">
          <Alert className="border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        </div>) : null}

        <Card>
          <CardContent className="p-1">
            {customerPendingOrders && customerPendingOrders.length > 0 ? (
              <div>
                <Table>
                  <TableHeader>
                    <TableRow className="bg-white hover:bg-gray-50">
                      <TableHead className="whitespace-nowrap">Order ID</TableHead>
                      <TableHead className="whitespace-nowrap">Delivery Date</TableHead>
                      <TableHead className="whitespace-nowrap">Driver Name</TableHead>
                      <TableHead className="whitespace-nowrap text-right">
                        <p>Credit Balance (₹)</p>
                        <p className="text-base text-orange-600 font-semibold">( T: <ShowDecimalAsSubscript value={formatCurrency(totalCreditPendingAmount)} decFontSize="11px" /> )</p>
                      </TableHead>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {customerPendingOrders.map((customer) => {
                      return (
                        <TableRow key={customer.orderGroupId} className="bg-white hover:bg-white">
                          <TableCell className="">{customer.orderGroupId}</TableCell>
                          <TableCell className="whitespace-nowrap">{formatDate(customer.deliveryDate)}</TableCell>
                          <TableCell className="">{customer.driverName}</TableCell>
                          <TableCell className="text-right">
                            {customer.creditPendingAmount ? <ShowDecimalAsSubscript value={formatCurrency(customer.creditPendingAmount)} decFontSize="11px" /> : "-"}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <User className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-lg font-medium text-gray-600">No credits found</p>
                    <p className="text-sm text-gray-500">No credit information available for this customer</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Dialog open={openModalFrom} onOpenChange={setOpenModalFrom}>
        <DialogOverlay className="bg-black/10" />
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Collect Cash</DialogTitle>
          </DialogHeader>
          <div className="sm:max-w-screen-sm max-h-96 overflow-auto rounded-md">
            <div className="space-y-5 p-2">
              <div className="bg-yellow-50 text-yellow-600 rounded-lg border border-yellow-200">
                <p className="text-sm font-medium p-2">Collect ₹ <ShowDecimalAsSubscript value={formatCurrency(selectedAmount)} decFontSize="11px" />  from {customerDetails?.buyerName}</p>
              </div>
              <div>
                <label>Payment Type</label>
                <div className="flex gap-5">
                  <div className="flex items-center">
                    <Input
                      type="radio"
                      id="fullPayment"
                      value="full"
                      checked={paymentType === 'full'}
                      onChange={() => {
                        setPaymentType('full');
                        setSelectedAmount(totalCreditPendingAmount);
                      }}
                      className="mr-2"
                    />
                    <label htmlFor="fullPayment">Full</label>
                  </div>
                  <div className="flex items-center">
                    <Input
                      type="radio"
                      id="partialPayment"
                      value="partial"
                      checked={paymentType === 'partial'}
                      onChange={() => setPaymentType('partial')}
                      className="mr-2"
                    />
                    <label htmlFor="partialPayment">Partial</label>
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <label>Amount to Receive (₹)</label>
                <Input
                  type="number"
                  value={selectedAmount}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (paymentType === 'partial') {
                      setSelectedAmount(value);
                    }
                  }}
                  disabled={paymentType === 'full'}
                  step="0.01"
                  className={paymentType === 'full' ? 'bg-gray-100' : ''}
                />
                {paymentType === 'partial' && selectedAmount <= 0 && (
                  <p className="text-sm text-red-500">Amount must be greater than 0</p>
                )}
                {paymentType === 'partial' && selectedAmount > totalCreditPendingAmount && (
                  <p className="text-sm text-red-500">Amount cannot exceed total pending amount</p>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenModalFrom(false)}>
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={paymentType === 'partial' && (selectedAmount <= 0 || selectedAmount > totalCreditPendingAmount)}
            >
              Collect
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
