import { ApiResponse } from "~/types/api/Api"
import { API_BASE_URL, apiRequest } from "~/utils/api"

export type DriverCreditSummary = {
  driverUserId: number,
  driverName: string,
  totalReceivedAmount: number,
  totalCreditPendingAmount: number
}

export type BuyerCreditSummary = {
  nBuyerId: number,
  buyerName: string,
  locality?: string,
  totalCollection?: number,
  totalCreditPending?: number,
  ogCashTracker?: CashTracker[]
}

export type CashTracker = {
  orderGroupId: number,
  deliveryDate: string,
  cashTrackerId?: number,
  collectionTime?: string,
  codCollected?: number,
  creditPendingCollected?: number,
  creditPendingAmount?: number
}

export async function getDriverCreditSummary(
  request?: Request
): Promise<ApiResponse<DriverCreditSummary[]>> {
  try {
    const response = await apiRequest<DriverCreditSummary[]>(
      `${API_BASE_URL}/bc/seller/drivers/credit-summary`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch driver credit summary");
    }
  } catch (err) {
    throw new Error("Failed to fetch driver credit summary");
  }
}

export async function getBuyerCreditSummary(
  driverUserId: number,
  request?: Request
): Promise<ApiResponse<BuyerCreditSummary[]>> {
  try {
    const response = await apiRequest<BuyerCreditSummary[]>(
      `${API_BASE_URL}/bc/seller/drivers/${driverUserId}/cash-collection-summary`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch buyer credit summary");
    }
  } catch (err) {
    throw new Error("Failed to fetch buyer credit summary");
  }
}

export async function recieveSellerCashFromDriver(
  ctSelected: number[],
  driverUserId: number,
  request?: Request
): Promise<ApiResponse<void>> {
  try {
    const response = await apiRequest<void>(
      `${API_BASE_URL}/bc/seller/recieve-sellercashfromdriver`,
      "PUT",
      { cashTrackerIds: ctSelected, receivedFromUserId: driverUserId },
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to recieve cash from driver");
    }
  } catch (err) {
    throw new Error("Failed to recieve cash from driver");
  }
}