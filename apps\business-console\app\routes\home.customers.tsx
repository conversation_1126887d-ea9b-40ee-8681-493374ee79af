
import { useState } from 'react'
import { <PERSON><PERSON> } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@components/ui/table"
import { Tabs, TabsList, TabsTrigger } from "@components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@components/ui/dialog"
import { Check, Edit, Eye, EyeOff, Plus, RefreshCcw, Upload, X, Send, ArrowDownWideNarrow, ArrowUp, ArrowDown, Save, Pencil, Info } from "lucide-react"
import { Form, Link, useFetcher, useLoaderData, useNavigate } from "@remix-run/react"
import type { ActionFunction, LoaderFunction, TypedResponse } from "@remix-run/node"
import { json } from "@remix-run/node"
import { getSession } from "@utils/session.server"
import type { User } from "~/types"
import { getBuyerSummary, updateFbDiscountPrice } from "@services/businessConsoleService"
import { BuyerSummaryDetailsResponseItem } from "~/types/api/businessConsoleService/BuyerSummaryDetailsResponseItem"
import { withAuth, withResponse } from "@utils/auth-utils"
import Pagination from '~/components/ui/oldPagination'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger, } from '~/components/ui/tooltip'
import SpinnerLoader from '~/components/loader/SpinnerLoader'
import { Label } from '~/components/ui/label'

interface LoaderData {
    data: BuyerSummaryDetailsResponseItem[];
    currentPage: number;
    hasNextPage: boolean;
    hasMoreData: boolean;
    tabValue: string;
    sortByvalue: string;
    searchBy: string;
    sortByOrder: string;
}
interface ErrorResponse {
    error: string;
}
export const loader = withAuth(async ({ user, request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "0");
    const tabValue = url.searchParams.get("tabValue") || "all";
    const sortBy = url.searchParams.get("sortBy") || "buyerName";
    const searchBy = url.searchParams.get("searchBy") || "";
    const sortByOrder = url.searchParams.get("sortByOrder") || "asc";
    const validSearchBy = searchBy && searchBy.length >= 3 ? searchBy : "";
    const pageSize = 50;
    try {
        const response = await getBuyerSummary(user.userId, page, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, undefined, request);
        const hasNextPage = (response.data?.length ?? 0) >= pageSize;
        const nextPageResponse = await getBuyerSummary(user.userId, page + 1, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, undefined, request);
        const hasMoreData = (nextPageResponse.data ?? []).length > 0;
        return withResponse({
            data: response.data,
            currentPage: page,
            hasNextPage,
            hasMoreData,
            tabValue,
            sortBy,
            searchBy,
            sortByOrder
        }, response.headers);
    } catch (error) {
        console.error("Buyer summary error:", error);
        throw new Response("Failed to fetch buyer summary data", {
            status: 500
        });
    }
});

export const action = withAuth(async ({ request }) => {

    const formData = await request.formData();
    const intent = formData.get("_intent");
    const nBuyerId = Number(formData.get("nBuyerId"))
    const fBDiscount = Number(formData.get("fbDiscount"))

    if (intent === "UpdateFbDiscount") {

        try {
            const response = await updateFbDiscountPrice(nBuyerId, fBDiscount, request);

            return withResponse(
                { data: response.data }, response.headers
            )
        }
        catch (error) {

            throw new Response("Failed to updateFbDiscount", {
                status: 500
            });
        }
    }



})

export default function CustomersSection() {
    const { data: customers, currentPage, hasNextPage, hasMoreData, sortByvalue } = useLoaderData<LoaderData>()
    const [searchTerm, setSearchTerm] = useState('')
    const [sortBy, setSortBy] = useState("buyerName")
    const [sortOrder, setSortOrder] = useState('asc')
    const [activeTab, setActiveTab] = useState('all')
    const navigate = useNavigate();
    const [hiddenCustomers, setHiddenCustomers] = useState<number[]>([])
    const [selectedCustomer, setSelectedCustomer] = useState<BuyerSummaryDetailsResponseItem | null>(null)
    const [selectedTemplate, setSelectedTemplate] = useState('')
    const [selectedBuyerData, setSelectedBuyerData] = useState<BuyerSummaryDetailsResponseItem | null>(null);

    const templates = [
        { name: 'hello_mnet', displayName: 'Hello mNet' },
    ]

    const handleTabChange = (newTab: string) => {
        // Map tab labels to `tabValue`
        const tabMap: { [key: string]: string } = {
            all: "all",
            oneOrder: "one_order",
            frequent: "frequent_orders",
            zero_orders: "zero_orders",
        };

        const validTabValue = tabMap[newTab] || "all"; // Default to "all" if tab is not recognized
        setActiveTab(newTab);
        console.log(newTab, "444444444")

        // Navigate with the correct `tabValue`
        navigate(`?tabValue=${validTabValue}&page=0&sortBy=${sortBy}&sortByOrder=${sortOrder}`);
    };

    const handleSort = (newSort: string) => {
        setSortBy((prevSortBy) => {
            const isSameSort = prevSortBy === newSort;
            console.log(activeTab, "444444444")

            setSortOrder((prevSortOrder) => {
                const newOrder = isSameSort && prevSortOrder === "asc" ? "desc" : "asc";

                navigate(`?tabValue=${activeTab}&page=0&sortBy=${newSort}&sortByOrder=${newOrder}&searchBy=${searchTerm}`);

                return newOrder;
            });

            return newSort;
        });
    };

    const getSortIcon = (column: string) => {
        if (sortBy !== column) return null; // Only show the icon for the sorted column
        return sortOrder === "asc" ? <ArrowUp className="w-4 h-4 ml-1" /> : <ArrowDown className="w-4 h-4 ml-1" />;
    };
    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        const searchParam = value.length >= 3 ? `${value}` : "";
        navigate(`?tabValue=${activeTab}&page=0&sortBy=${sortBy}&sortByOrder=${sortOrder}&searchBy=${searchParam}`);
    };

    const toggleHidden = (id: number) => {
        setHiddenCustomers(prev =>
            prev.includes(id) ? prev.filter(customerId => customerId !== id) : [...prev, id]
        )
    }

    const filteredCustomers = customers

    const [fbDiscounts, setFbDiscounts] = useState<{ [key: number]: string }>({});
    const [isFbDis, setIsFbDis] = useState<{ [key: number]: boolean }>({});

    const handleChangeFbDiscount = (nBuyerId: number, val: string) => {
        if (/^\d*\.?\d*$/.test(val) || val === "") {  // Allow empty string
            setFbDiscounts((prev) => ({ ...prev, [nBuyerId]: val }));
        }
    };
    const handleEdit = (customer: any) => {
        setIsFbDis((prev) => ({ ...prev, [customer.nBuyerId]: true }));
    };


    const fbFetcher = useFetcher<BuyerSummaryDetailsResponseItem>()


    const handleSave = (nBuyerId: number,) => {
        const formData = new FormData()
        formData.append("_intent", "UpdateFbDiscount");
        formData.append("nBuyerId", nBuyerId.toString());
        formData.append("fbDiscount", fbDiscounts[nBuyerId]);
        fbFetcher.submit(formData, { method: "POST" })
        console.log(`Saving discount for Buyer ID ${nBuyerId}:`, fbDiscounts[nBuyerId]);
        const updatedBuyer = customers.find(customer => customer.nBuyerId === nBuyerId);
        if (updatedBuyer) {
            setSelectedBuyerData({ ...updatedBuyer, discount: Number(fbDiscounts[nBuyerId]) });
        }

        setIsFbDis((prev) => ({ ...prev, [nBuyerId]: false }));
    };
    const [infoOpen, setInfoOpen] = useState(false);
    const handleInfoOpen = () => {
        setInfoOpen(true)
    }
    const handleClearInfo = () => {
        setInfoOpen(false);

    }

    return (
        <div className="container mx-auto p-6">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">My Customers</h1>
                {/* <div className="flex gap-2">
                    <Button variant="outline" size="icon">
                        <Plus className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                        <Upload className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                        <RefreshCcw className="h-4 w-4" />
                    </Button>
                </div> */}
            </div>



            <TooltipProvider>
                <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-6">
                    <TabsList>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                {/* Wrap the button inside a div to ensure proper highlight */}
                                <div className="relative">
                                    <TabsTrigger value="all">All</TabsTrigger>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>All customers</TooltipContent>
                        </Tooltip>

                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="relative">
                                    <TabsTrigger value="frequent">Frequent customers</TabsTrigger>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>Customers who have placed more than 10 orders in the last 3months.</TooltipContent>
                        </Tooltip>

                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="relative">
                                    <TabsTrigger value="oneOrder">One order customers</TabsTrigger>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>Customers who have placed only one order in the last year </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="relative">
                                    <TabsTrigger value="zero_orders"> New Customers</TabsTrigger>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>Customers with no orders in the past 3 months</TooltipContent>
                        </Tooltip>


                    </TabsList>
                </Tabs>
            </TooltipProvider>
            <div className="flex justify-between mb-4">
                <Input
                    placeholder="Search by name or owner"
                    value={searchTerm}
                    onChange={searchTerm.length >= 3 ? handleSearch : (e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                />
                <Select value={sortBy} onValueChange={handleSort}>
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="buyerName">Name</SelectItem>
                        <SelectItem value="totalOrders">Number of Orders</SelectItem>
                        <SelectItem value="pendingAmount">Pending Balance</SelectItem>
                        <SelectItem value="lastOrderedDate">Order Duration Days</SelectItem>
                        <SelectItem value="fbDiscount">Contract Price ExpDate</SelectItem>
                        <SelectItem value="fbDiscount">F.B.Discount</SelectItem>

                    </SelectContent>
                </Select>
            </div>
            <Table >
                {fbFetcher.state !== "idle" &&
                    <SpinnerLoader loading={true} />}
                <TableHeader >
                    <TableRow  >
                        <TableHead className="cursor-pointer " onClick={() => handleSort("buyerName")}>
                            <span className='flex flex-row items-center gap-1'> <span>Name</span> <span>{getSortIcon("buyerName")}</span></span>
                        </TableHead>
                        {/* <TableHead className="cursor-pointer   " onClick={() => handleSort("totalOrders")}>
                            <span className='flex flex-row items-center gap-1'> <span>Area Name</span> {getSortIcon("totalOrders")} </span>
                        </TableHead>
                        <TableHead className="cursor-pointer   " onClick={() => handleSort("totalOrders")}>
                            <span className='flex flex-row items-center gap-1'> <span>Agent Name</span> {getSortIcon("totalOrders")} </span>
                        </TableHead> */}
                        {/* <TableHead className="text-left">Mobile Number</TableHead> */}

                        <TableHead className="cursor-pointer   " onClick={() => handleSort("totalOrders")}>
                            <span className='flex flex-row items-center gap-1'> <span>Num of Orders</span> {getSortIcon("totalOrders")} </span>
                        </TableHead>

                        <TableHead className="cursor-pointer " onClick={() => handleSort("pendingAmount")}>
                            <span className='flex flex-row items-center gap-1'>  <span>Pending Balance</span> {getSortIcon("pendingAmount")} </span>
                        </TableHead>

                        <TableHead className="cursor-pointer    " onClick={() => handleSort("lastOrderedDate")}>
                            <span className='flex flex-row items-center gap-1'>  <span>Order Duration Days</span> {getSortIcon("lastOrderedDate")} </span>
                        </TableHead>
                        <TableHead className="cursor-pointer    " onClick={() => handleSort("lastOrderedDate")}>
                            <span className='flex flex-row items-center gap-1'>  <span>Contract Price ExpDate</span> {getSortIcon("lastOrderedDate")} </span>
                        </TableHead>
                        <TableHead >
                            <span className='items-center gap-1' onClick={() => handleSort("fbDiscount")}>
                                <span className='flex flex-row items-center gap-1'><span>Freq Buyer Discount</span> {getSortIcon("fbDiscount")} </span>
                            </span>
                        </TableHead>

                    </TableRow>
                </TableHeader>

                <TableBody>
                    {filteredCustomers.map((customer) => {
                        const isHidden = hiddenCustomers.includes(customer.buyerId)



                        return (
                            <TableRow key={customer.buyerId}>


                                <TableCell>
                                    <div className='flex flex-row gap-2  items-center'>

                                        <Link to={`/home/<USER>/${customer.buyerId}`}
                                            className="text-blue-600 hover:underline">
                                            <div>{customer.buyerName !== "" ? customer.buyerName : "( Name not given )"}</div>
                                        </Link>
                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Info size={18} onClick={handleInfoOpen} className="cursor-pointer text-gray-600 hover:text-purple-600 transition-all" />
                                            </DialogTrigger>
                                            <DialogContent className="sm:max-w-fit p-5 rounded-lg">
                                                <DialogHeader>
                                                    <DialogTitle className="text-lg font-semibold text-gray-800">About Customer</DialogTitle>
                                                </DialogHeader>
                                                <div className="flex flex-col gap-3 mt-2">
                                                    <div className="flex flex-row gap-2">
                                                        <span className="text-sm text-gray-600">Buyer Name:</span>
                                                        <span className="text-base text-purple-800 font-semibold">{customer.buyerName}</span>
                                                    </div>
                                                    <div className="flex flex-row gap-2">
                                                        <span className="text-sm text-gray-600">Mobile Number:</span>
                                                        <span className="text-base text-purple-800 font-semibold">{customer.mobileNumber}</span>
                                                    </div>
                                                </div>
                                            </DialogContent>
                                        </Dialog>

                                    </div>

                                </TableCell>
                                {/* <TableCell>xxxxxxxxxxxxx</TableCell>
                                <TableCell>xxxxxxxxxxxxx</TableCell> */}
                                {/* <TableCell>{customer.mobileNumber}</TableCell> */}
                                <TableCell>{customer.totalOrders}</TableCell>
                                <TableCell>₹ {customer.pendingAmount.toLocaleString('en-IN')}</TableCell>
                                <TableCell>
                                    {customer.lastOrderedDate
                                        ? (isNaN(new Date(customer.lastOrderedDate).getTime())
                                            ? '-'
                                            : Math.floor((new Date().getTime() - new Date(customer.lastOrderedDate).getTime()) / (1000 * 60 * 60 * 24)) + ' days')
                                        : '-'}
                                </TableCell>
                                <TableCell
                                    className={`${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split("-").reverse().join("-")) < new Date() ? "bg-orange-500" : ""} ${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split("-").reverse().join("-")) < new Date() ? 'text-blue-500' : 'text-gray-500'} ${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split("-").reverse().join("-")) < new Date() ? 'rounded-full' : ''}`}
                                >
                                    {customer.contractPriceExpDate || "-"}
                                </TableCell>
                                <TableCell className='flex flex-row items-center gap-2'>
                                    {isFbDis[customer.nBuyerId] ? (
                                        <div className="flex flex-row justify-center items-center gap-3">
                                            <Input
                                                value={String(fbDiscounts[customer.nBuyerId] ?? customer.fbDiscount ?? "")}
                                                onChange={(e) => {
                                                    let val = e.target.value;

                                                    // Allow only numbers with optional decimals
                                                    if (!/^\d*\.?\d*$/.test(val)) return;

                                                    // Convert to number for validation
                                                    let numVal = parseFloat(val);

                                                    // Enforce range (0 to 100)
                                                    if (numVal > 100) numVal = 100;
                                                    if (numVal < 0 || isNaN(numVal)) numVal = 0;

                                                    handleChangeFbDiscount(customer.nBuyerId, String(numVal));
                                                }}
                                                disabled={!isFbDis[customer.nBuyerId]}
                                                type="number"
                                                min="0"
                                                max="100"
                                                step="0.01" // Allows decimals
                                            />
                                            <Save
                                                size={24}
                                                onClick={() => handleSave(customer.nBuyerId)}
                                                className="cursor-pointer text-green-500"
                                            />
                                            <X
                                                color="red"
                                                size={24}
                                                className="cursor-pointer text-red-500"
                                                onClick={() => setIsFbDis({})} // Close all inputs
                                            />
                                        </div>

                                    ) : (
                                        <div className='flex flex-row gap-3 items-center justify-center'>
                                            <span>{customer.fbDiscount > 0 ? `${customer.fbDiscount} %` : "-"}</span>
                                            <Pencil
                                                size={15}
                                                onClick={() => setIsFbDis({ [customer.nBuyerId]: true })} // Open only this row
                                                className="cursor-pointer text-blue-500"
                                            />
                                        </div>
                                    )}
                                </TableCell>

                                {/* <TableCell>
                                    <div className="flex gap-2">
                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => setSelectedCustomer(customer)}
                                                >
                                                    <Send className="h-4 w-4" />
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent>
                                                <DialogHeader>
                                                    <DialogTitle>Send WhatsApp Message</DialogTitle>
                                                </DialogHeader>
                                                <Form method="post">
                                                    <input
                                                        type="hidden"
                                                        name="phoneNo"
                                                        value={selectedCustomer?.mobileNumber || ''}
                                                    />
                                                    <div className="py-4">
                                                        <h3 className="mb-2 font-semibold">Select a template:</h3>
                                                        <Select
                                                            value={selectedTemplate}
                                                            onValueChange={setSelectedTemplate}
                                                        >
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Choose a template" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {templates.map((template) => (
                                                                    <SelectItem key={template.name}
                                                                        value={template.name}>
                                                                        {template.displayName}
                                                                    </SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                        <input
                                                            type="hidden"
                                                            name="templateName"
                                                            value={selectedTemplate}
                                                        />
                                                    </div>
                                                    <div className="flex justify-end">
                                                        <Button
                                                            type="submit"
                                                            disabled={!selectedTemplate}
                                                        >
                                                            Send Message
                                                        </Button>
                                                    </div>
                                                </Form>
                                            </DialogContent>
                                        </Dialog>
                                        <Button variant="ghost" size="icon"
                                            onClick={() => toggleHidden(customer.buyerId)}>
                                            {isHidden ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                        </Button>
                                        <Button variant="ghost" size="icon">
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </TableCell> */}
                            </TableRow>
                        )
                    })}
                </TableBody>
            </Table>
            <Pagination currentPage={currentPage} hasMoreData={hasMoreData}
                tabValue={activeTab}
                sortBy={sortBy}
                searchBy={searchTerm}
                sortByOrder={sortOrder}
            />

        </div>
    )
}
