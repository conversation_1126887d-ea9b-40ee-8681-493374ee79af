import { ApiResponse } from "~/types/api/Api";
import { TripDto, TripOrderDto, TripSummaryDto } from "~/types/api/businessConsoleService/MyTrips";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getSellerTrips(
  date: string,
  request?: Request
): Promise<ApiResponse<TripSummaryDto[]>> {
  try {
    const response = await apiRequest<TripSummaryDto[]>(
      `${API_BASE_URL}/bc/seller/trips/date/${date}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch trip summary");
    }
  } catch (err) {
    throw new Error("Failed to fetch trip summary");
  }
}

export async function getTrip(
  tripId: number,
  role?: string,
  request?: Request
): Promise<ApiResponse<TripDto>> {
  try {
    const response = await apiRequest<TripDto>(
      `${API_BASE_URL}/bc/${role ? role : "mnetadmin"}/trips/${tripId}/orders`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch trip details");
    }
  } catch (err) {
    throw new Error("Failed to fetch trip details");
  }
}

export async function getTripOrder(
  orderGroupId: number,
  request?: Request
): Promise<ApiResponse<TripOrderDto>> {
  try {
    const response = await apiRequest<TripOrderDto>(
      `${API_BASE_URL}/bc/seller/orders/${orderGroupId}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch trip order");
    }
  } catch (err) {
    throw new Error("Failed to fetch trip order");
  }
} 
