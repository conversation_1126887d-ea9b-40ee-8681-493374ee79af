import { AddOnGroup } from "./MyItemList";

export interface TripSummaryDto {
  tripId: number;
  sellerId: number;
  sellerName: string;
  tripStatus: string;
  driverName: string;
  totalOrders: number;
  totalDeliveredOrders: number;
  deliveryDate: string;
  cancelledOrders: number;
  totalWeight: number;
  totalCancelledWeight: number;
  areas: string[];
}

export type TripDto = {
  tripId: number;
  inventoryId: number;
  sellerId: number;
  sellerName: string;
  sellerMobileNumber: string;
  driverId: number;
  driverName: string;
  driverMobileNumber: string;
  serviceAreaList: string;
  completedOrderCount: number;
  totalOrderCount: number;
  completedWeight: number;
  totalWeight: number;
  cashCollected: number;
  ordersToPick: number;
  ordersToDispatch: number;
  ordersToDeliver: number;
  nextOrderToPick: number;
  nextOrderToDispatch: number;
  nextOrderToDeliver: number;

  orders: TripOrderDto[];

  totalBookedWeight: number;
  totalCurrentOrdersWeight: number;
  totalDispatchedWeight: number;
  totalDeliveredWeight: number;
  totalReturnedWeight: number;
  totalResoldOrShortageWeight: number;
  totalDeliveryPendingWeight: number;
  totalCancelledWeight: number;
  totalBookedAmount: number;
  totalDispatchedAmount: number;
  totalDeliveredAmount: number;
  totalReturnedAmount: number;
  totalResoldOrShortageAmount: number;
  totalDeliveryPendingAmount: number;
  deliveredCodAmount: number;
  deliveredCreditGiven: number;

  salesExecutiveName: string;
  salesExecutiveMobile: string;
  deliveryDate: string; // LocalDate

  userType: string;
  userName: string;

  tripStatus: string;

  returnItems: TripReturnItemDetailDto[];

  saiId: number;
  truckNumber: string;
  dispatchedBy: string;
  dispatchedTime: string; // LocalDateTime
  dispatchedKmReading: number;
  dispatchedBoxCount: number;
  minWeight: number;
  returnedBy: string;
  returnedTime: string; // LocalDateTime
  returnedKmReading: number;
  returnedBoxCount: number;
  tripKms: number;
  tripDuration: number;
  maxKms: number;
  maxDuration: number;
  basicRate: number;
  extraKmRate: number;
  extraTimeRate: number;
  basicCost: number;
  excessKmCharge: number;
  excessTimeCharge: number;
  totalTripCharge: number;
  codTotal: number;
  cashReceived: number;
  cashShortage: number;
  boxesShortageCount: number;
  boxesPenaltyAmount: number;
  netTripPayment: number;
  vendorId: number;
  vendorName: string;
  isSupplyTrip: boolean;

  mapUrl: string;
  agents: string;
  pickerId: number;
  pickerName: string;
  pickerMobile: string;
  itemPickEnabled: boolean;
  deliveryType: string;
  ondcDomain: string;
  pos: string;
};

export type TripOrderDto = {
  orderGroupId: number;
  inventoryId: number;
  buyerName: string;
  buyerId: number;
  sellerId: number;
  orderStatus: string;
  cashCollectionAmount: number;
  creditAmount: number;
  address: string;
  latitude: string;
  longitude: string;
  primaryContactNumber: string;
  secondContactNumber: string;
  buyerLocality: string;
  serviceAreaName: string;

  orderAmount: number;
  deliveryCharge: number;
  totalAmount: number;
  discountAmount: number;
  itemCount: number;
  weight: number;
  creditAllowed: boolean;
  cashAllowed: boolean;
  orderDetail: TripOrderDetailDto[];

  delayPayment: boolean;
  delayPaymentPaid: boolean;
  delayPaymentPaidDirect: boolean;
  creditPendingAmount: number;

  deliveredTime: string; // LocalDateTime
  deliveredBy: string;
  deliveredByContactNumber: string;
  sellerName: string;

  tripId: number;
  tripSeqNumber: number;

  salesExecutiveName: string;
  salesExecutiveMobile: string;
  deliveryDate: string; // LocalDate

  codTimeout: string; // LocalDateTime
  creditTimeout: string; // LocalDateTime

  shopOpenTime: number;
  totalBoxes: number;
  boxesGiven: number;
  boxesTaken: number;
  boxesBalance: number;
  approxPricing: boolean;
  pickedItemCount: number;
  pickedItemWeight: number;
  itemPickEnabled: boolean;
  totalOrderBoxes: number;
  totalOrderBags: number;
  deliveryType: string;
  delAssignedToName: string;
  delAssignedToUserId: number;
  defaultAddress?: AddressDto;
  sellerMessage?: string;
  totalTaxAmount?: number;
  packagingCharges?: number;
  platformFee?: number;
  ondcDomain?: string;
  estDeliveryTime?: string;
  pos?: string;
  networkType?: string;
  riderName?: string;
  riderPhone?: string;
  logisticProvider?: string;
  fulfillmentType?: string;
  logStatus?: string;
  pickupOtp?: string;
  deliveryOtp?: string;
  createdOn?: string; // LocalDateTime
  sellerLatitude: number;
  sellerLongitude: number;
  sellerOndcAddress?: OndcAddressDto1;
  ondcOrderId?: string;
  sellerContactNumber?: string;
};

export type TripOrderDetailDto = {
  orderId: number;
  itemId: number;
  itemName: string;
  packaging: string;
  itemUrl: string;
  itemRegionalLanguageName: string;
  unit: string;
  qty: number;
  price: number;
  amount: number;
  status: string;
  isReturnAllowed: boolean;
  cancelledQty: number;
  boxes: number;
  itemPicked: boolean;
  addOns: AddOnGroup[];
  posItemId: string;
  diet: string;
  variationSId?: string;
  variationName?: string;
  strikeOffAmount: number;
  itemTaxAmount?: number;
  variationId?: string;
  freeItem: boolean;
};

export type TripReturnItemDetailDto = {
  id: number;
  siiId: number;
  tripId: number;
  itemName: string;
  itemUrl: string;
  itemUnit: string;
  dispatchedQty: number;
  deliveredQty: number;
  totalReturnedQty: number;
  totalReturnedAmount: number;
  returnedPrice: number;
  handoverQty: number;
  shortageQty: number;
  shortageAmount: number;
};

export type AddressDto = {
  addressId: number;
  businessId: number;
  name: string;
  address: string;
  latitude: string;
  longitude: string;
  isDefault: boolean,
  buyerInServiceArea: boolean;
}

export type OndcAddressDto1 = {
  door?: string;
  name?: string;
  building?: string;
  street?: string;
  locality?: string;
  ward?: string;
  city?: string;
  state?: string;
  country?: string;
  areaCode?: string;
} 