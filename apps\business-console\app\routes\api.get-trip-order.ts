import { json, LoaderFunction } from "@remix-run/node";
import { getTripOrder } from "~/services/myTripsService";

export const loader: LoaderFunction = async ({ request }) => {
  const url = new URL(request.url);
  const orderGroupId = Number(url.searchParams.get("orderId"));

  if (!orderGroupId) {
    return json({ error: "Order Id is required" }, { status: 400 });
  }

  try {
    const tripOrder = await getTripOrder(orderGroupId, request);
    return json({ tripOrderDetails: tripOrder.data });
  } catch (error) {
    return json(
      { error: "Fetching trip order failed" },
      { status: 500 }
    );
  }
};
