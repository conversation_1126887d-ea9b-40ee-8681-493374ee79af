import { TripDto, TripOrderDto } from "~/types/api/businessConsoleService/MyTrips";
import { Dialog, DialogOverlay, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "../ui/dialog";
import { useEffect, useState } from "react";
import { Button } from "../ui/button"
import { Separator } from "../ui/separator"
import { Loader2, User, Truck } from "lucide-react"
import { TripOrderDetailsModal } from "./TripOrderDetailsModal";
import ShowDecimalAsSubscript from "../common/ShowDecimalAsSubscript";
import { format, toZonedTime } from "date-fns-tz";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table";

/**
 * Trip Details Modal
 * @param tripId - Trip Id
 * @param tripDetails - Trip Details
 * @param onClose - Close Modal Callback
 * @returns
 */
export function TripDetailsModal({
  tripId,
  tripDetails,
  onClose,
}: {
  tripId: number | null
  tripDetails: TripDto | null
  onClose: () => void
}) {
  const [trip, setTrip] = useState<TripDto | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTrip = async (tripId: number) => {
    setLoading(true)
    try {
      const tripResponse = await fetch(`/api/get-trip?tripId=${tripId}&role=seller`)
      if (!tripResponse.ok) throw new Error("Failed to fetch trip details")
      const data = await tripResponse.json()
      setTrip(data.tripDetails)
      setError(null)
    } catch (err) {
      setTrip(null)
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (tripDetails && tripDetails.tripId) {
      setTrip(tripDetails)
    } else if (tripId) {
      fetchTrip(tripId)
    }
  }, [tripId, tripDetails])

  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null)
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<TripOrderDto | null>(null)

  return (
    <Dialog
      open={!!tripId}
      onOpenChange={(open) => {
        if (!open) {
          onClose()
        }
      }}
    >
      <DialogOverlay className="bg-black/10" />
      <DialogContent className="max-w-5xl rounded-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Trip Details
          </DialogTitle>
        </DialogHeader>

        <div className="max-h-[75vh] overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading trip details...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-destructive">
              <p>Error loading trip details: {error}</p>
            </div>
          ) : null}

          {trip && !loading && (
            <div className="space-y-4">

              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10">
                <div className="space-y-2">
                  <h3 className="font-semibold">
                    Driver Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Trip ID:&nbsp;</span> {trip.tripId}
                    </p>
                    <p>
                      <span className="font-medium">Name:&nbsp;</span> {trip.driverName}
                    </p>
                    <p>
                      <span className="font-medium">Mobile:&nbsp;</span> {trip.driverMobileNumber}
                    </p>
                    <p>
                      <span className="font-medium">Truck:&nbsp;</span> {trip.truckNumber}
                    </p>
                    <p>
                      <span className="font-medium">Areas:&nbsp;</span> {trip.serviceAreaList}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">
                    Delivery Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Delivery Date:&nbsp;</span> {formatDate(trip.deliveryDate)}
                    </p>
                    <p>
                      <span className="font-medium">Trip Status:&nbsp;</span> <span className={trip.tripStatus === "Dispatched" ? "text-red-500" : trip.tripStatus === "Open" ? "text-orange-500" : "text-green-600"}>{trip.tripStatus}</span>
                    </p>
                    <p>
                      <span className="font-medium">Total Orders:&nbsp;</span> {trip.totalOrderCount}
                    </p>
                    <p>
                      <span className="font-medium">Completed Orders:&nbsp;</span> {trip.completedOrderCount}
                    </p>
                    <p>
                      <span className="font-medium">Total Weight:&nbsp;</span> <ShowDecimalAsSubscript value={formatCurrency(trip.totalWeight)} decFontSize="10px" /> kg
                    </p>
                    <p>
                      <span className="font-medium">Completed Weight:&nbsp;</span> <ShowDecimalAsSubscript value={formatCurrency(trip.completedWeight)} decFontSize="10px" /> kg
                    </p>
                    <p>
                      <span className="font-medium">Cash Collected:&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(trip.cashCollected)} decFontSize="10px" />
                    </p>
                    <p>
                      <span className="font-medium">Next Order:&nbsp;</span> {trip.nextOrderToDeliver}
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Weight & Amount Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10">
                <div className="space-y-2">
                  <h3 className="font-semibold">
                    Weight Summary
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Booked:</span>
                      <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(trip.totalBookedWeight)} decFontSize="10px" /> kg</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Dispatched:</span>
                      <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(trip.totalDispatchedWeight)} decFontSize="10px" /> kg</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivered:</span>
                      <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(trip.totalDeliveredWeight)} decFontSize="10px" /> kg</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Returned:</span>
                      <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(trip.totalReturnedWeight)} decFontSize="10px" /> kg</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cancelled:</span>
                      <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(trip.totalCancelledWeight)} decFontSize="10px" /> kg</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Pending:</span>
                      <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(trip.totalDeliveryPendingWeight)} decFontSize="10px" /> kg</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold">
                    Amount Summary
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Booked:</span>
                      <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(trip.totalBookedAmount)} decFontSize="10px" /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Dispatched:</span>
                      <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(trip.totalDispatchedAmount)} decFontSize="10px" /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivered:</span>
                      <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(trip.totalDeliveredAmount)} decFontSize="10px" /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Returned:</span>
                      <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(trip.totalReturnedAmount)} decFontSize="10px" /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Pending:</span>
                      <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(trip.totalDeliveryPendingAmount)} decFontSize="10px" /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>COD Collected:</span>
                      <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(trip.deliveredCodAmount)} decFontSize="10px" /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credit Given:</span>
                      <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(trip.deliveredCreditGiven)} decFontSize="10px" /></span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Dispatch & Sales Executive Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10">
                <div className="space-y-2">
                  <h3 className="font-semibold">Dispatch Details</h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Dispatched By:</span> {trip.dispatchedBy}
                    </p>
                    <p>
                      <span className="font-medium">Time:</span> {formatTime(trip.dispatchedTime)}
                    </p>
                    <p>
                      <span className="font-medium">KM Reading:</span> {trip.dispatchedKmReading}
                    </p>
                    <p>
                      <span className="font-medium">Box Count:</span> {trip.dispatchedBoxCount}
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Orders */}
              <div className="space-y-2">
                <h3 className="font-semibold">Order Details ({trip.orders?.length})</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-xs h-10">Order ID</TableHead>
                      <TableHead className="text-xs h-10">Buyer</TableHead>
                      <TableHead className="text-xs h-10">Status</TableHead>
                      <TableHead className="text-xs h-10">Weight</TableHead>
                      <TableHead className="text-xs h-10 text-right">Amount (₹)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {trip.orders?.map((order) => (
                      <TableRow key={order.orderGroupId}>
                        <TableCell className="text-xs h-10 text-blue-500 cursor-pointer" onClick={() => { setSelectedOrderId(order.orderGroupId); setSelectedOrderDetails(order) }}>{order.orderGroupId}</TableCell>
                        <TableCell className="text-xs h-10">{order.buyerName}</TableCell>
                        <TableCell className="text-xs h-10">{order.orderStatus}</TableCell>
                        <TableCell className="text-xs h-10 whitespace-nowrap"><ShowDecimalAsSubscript value={formatCurrency(order.weight)} decFontSize="10px" /> kg</TableCell>
                        <TableCell className="text-xs h-10 text-right"><ShowDecimalAsSubscript value={formatCurrency(order.totalAmount)} decFontSize="10px" /></TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {trip.returnItems?.length > 0 && (
                <>
                  <Separator />

                  <div className="space-y-2">
                    <h3 className="font-semibold">Return Items ({trip.returnItems?.length})</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="text-xs h-10">Item</TableHead>
                          <TableHead className="text-xs h-10 text-center">Dispatched Qty (kg)</TableHead>
                          <TableHead className="text-xs h-10 text-center">Delivered Qty (kg)</TableHead>
                          <TableHead className="text-xs h-10 text-center">Returned Qty (kg)</TableHead>
                          <TableHead className="text-xs h-10 text-right">Returned Amount (₹)</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {trip.returnItems?.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="text-xs h-10">{item.itemName}</TableCell>
                            <TableCell className="text-xs h-10 text-center">{item.dispatchedQty}</TableCell>
                            <TableCell className="text-xs h-10 text-center">{item.deliveredQty}</TableCell>
                            <TableCell className="text-xs h-10 text-center">{item.totalReturnedQty === 0 ? "-" : item.totalReturnedQty}</TableCell>
                            <TableCell className="text-xs h-10 text-right">{formatCurrency(item.totalReturnedAmount)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </>
              )}
            </div>
          )}
        </div>

        <TripOrderDetailsModal
          orderId={selectedOrderId}
          orderDetails={selectedOrderDetails}
          onClose={() => {
            setSelectedOrderId(null)
            setSelectedOrderDetails(null)
          }}
        />

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

const formatTime = (dateString: string) => {
  const istTime = toZonedTime(dateString, "Asia/Kolkata");
  return format(istTime, "dd MMM, hh:mm a");
};

function formatCurrency(amount: number) {
  return new Intl.NumberFormat('en-IN', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}