import { LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { ArrowLeft, User } from "lucide-react";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription } from "~/components/ui/card";
import { getTrip } from "~/services/myTripsService";
import { TripDto, TripOrderDto } from "~/types/api/businessConsoleService/MyTrips";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { format, toZonedTime } from "date-fns-tz";
import ShowDecimalAsSubscript from "~/components/common/ShowDecimalAsSubscript";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { useState } from "react";
import { TripOrderDetailsModal } from "~/components/mytrip/TripOrderDetailsModal";

interface LoaderData {
  tripDetails: TripDto | null;
  error?: string;
}

export const loader: LoaderFunction = withAuth(async ({ request, params }) => {
  const tripId = params?.id;
  if (!tripId) {
    throw new Response("Invalid trip ID", { status: 400 });
  }
  try {
    const response = await getTrip(Number(tripId), "seller", request);
    return withResponse({
      tripDetails: response.data
    }, response?.headers);
  } catch (error) {
    return withResponse({
      tripDetails: null,
      error: "Failed to load trip details"
    }, new Headers());
  }
});


function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

const formatTime = (dateString: string) => {
  const istTime = toZonedTime(dateString, "Asia/Kolkata");
  return format(istTime, "dd MMM, hh:mm a");
};

function formatCurrency(amount: number) {
  return new Intl.NumberFormat('en-IN', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

export default function MyTrip() {
  const { tripDetails, error } = useLoaderData<LoaderData>();
  const navigate = useNavigate();

  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null)
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<TripOrderDto | null>(null)

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-7xl">
      <div className="space-y-4">
        <div>
          <Button variant="secondary" size="sm" onClick={() => navigate(-1)} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Trips
          </Button>
          <div>
            <h1 className="text-lg sm:text-2xl font-bold text-gray-900">
              Trip Details
            </h1>
          </div>
        </div>

        {error ? (<div className="p-6">
          <Alert className="border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        </div>) : null}

        <Card>
          <CardContent className="p-4">
            {tripDetails ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10">
                  <div className="space-y-2">
                    <h3 className="font-semibold">
                      Driver Information
                    </h3>
                    <div className="text-sm space-y-1">
                      <p>
                        <span className="font-medium">Trip ID:&nbsp;</span> {tripDetails.tripId}
                      </p>
                      <p>
                        <span className="font-medium">Name:&nbsp;</span> {tripDetails.driverName}
                      </p>
                      <p>
                        <span className="font-medium">Mobile:&nbsp;</span> {tripDetails.driverMobileNumber}
                      </p>
                      <p>
                        <span className="font-medium">Truck:&nbsp;</span> {tripDetails.truckNumber}
                      </p>
                      <p>
                        <span className="font-medium">Areas:&nbsp;</span> {tripDetails.serviceAreaList}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold">
                      Delivery Information
                    </h3>
                    <div className="text-sm space-y-1">
                      <p>
                        <span className="font-medium">Delivery Date:&nbsp;</span> {formatDate(tripDetails.deliveryDate)}
                      </p>
                      <p>
                        <span className="font-medium">Trip Status:&nbsp;</span> <span className={`font-semibold ${tripDetails.tripStatus === "Dispatched" ? "text-red-500" : tripDetails.tripStatus === "Open" ? "text-orange-500" : "text-green-600"}`}>{tripDetails.tripStatus}</span>
                      </p>
                      <p>
                        <span className="font-medium">Total Orders:&nbsp;</span> {tripDetails.totalOrderCount}
                      </p>
                      <p>
                        <span className="font-medium">Completed Orders:&nbsp;</span> {tripDetails.completedOrderCount}
                      </p>
                      <p>
                        <span className="font-medium">Total Weight:&nbsp;</span> <ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalWeight)} decFontSize="10px" /> kg
                      </p>
                      <p>
                        <span className="font-medium">Completed Weight:&nbsp;</span> <ShowDecimalAsSubscript value={formatCurrency(tripDetails.completedWeight)} decFontSize="10px" /> kg
                      </p>
                      <p>
                        <span className="font-medium">Cash Collected:&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(tripDetails.cashCollected)} decFontSize="10px" />
                      </p>
                      <p>
                        <span className="font-medium">Next Order:&nbsp;</span> {tripDetails.nextOrderToDeliver}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10">
                  <div className="space-y-2">
                    <h3 className="font-semibold">
                      Weight Summary
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Booked:</span>
                        <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalBookedWeight)} decFontSize="10px" /> kg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Dispatched:</span>
                        <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalDispatchedWeight)} decFontSize="10px" /> kg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Delivered:</span>
                        <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalDeliveredWeight)} decFontSize="10px" /> kg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Returned:</span>
                        <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalReturnedWeight)} decFontSize="10px" /> kg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cancelled:</span>
                        <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalCancelledWeight)} decFontSize="10px" /> kg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Pending:</span>
                        <span className="font-medium"><ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalDeliveryPendingWeight)} decFontSize="10px" /> kg</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-semibold">
                      Amount Summary
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Booked:</span>
                        <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalBookedAmount)} decFontSize="10px" /></span>
                      </div>
                      <div className="flex justify-between">
                        <span>Dispatched:</span>
                        <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalDispatchedAmount)} decFontSize="10px" /></span>
                      </div>
                      <div className="flex justify-between">
                        <span>Delivered:</span>
                        <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalDeliveredAmount)} decFontSize="10px" /></span>
                      </div>
                      <div className="flex justify-between">
                        <span>Returned:</span>
                        <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalReturnedAmount)} decFontSize="10px" /></span>
                      </div>
                      <div className="flex justify-between">
                        <span>Pending:</span>
                        <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(tripDetails.totalDeliveryPendingAmount)} decFontSize="10px" /></span>
                      </div>
                      <div className="flex justify-between">
                        <span>COD Collected:</span>
                        <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(tripDetails.deliveredCodAmount)} decFontSize="10px" /></span>
                      </div>
                      <div className="flex justify-between">
                        <span>Credit Given:</span>
                        <span className="font-medium">₹ <ShowDecimalAsSubscript value={formatCurrency(tripDetails.deliveredCreditGiven)} decFontSize="10px" /></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10">
                  <div className="space-y-2">
                    <h3 className="font-semibold">Dispatch Details</h3>
                    <div className="text-sm space-y-1">
                      <p>
                        <span className="font-medium">Dispatched By:</span> {tripDetails.dispatchedBy}
                      </p>
                      <p>
                        <span className="font-medium">Time:</span> {formatTime(tripDetails.dispatchedTime)}
                      </p>
                      <p>
                        <span className="font-medium">KM Reading:</span> {tripDetails.dispatchedKmReading}
                      </p>
                      <p>
                        <span className="font-medium">Box Count:</span> {tripDetails.dispatchedBoxCount}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold">Order Details ({tripDetails.orders?.length})</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-xs h-10">Order ID</TableHead>
                        <TableHead className="text-xs h-10">Buyer</TableHead>
                        <TableHead className="text-xs h-10">Status</TableHead>
                        <TableHead className="text-xs h-10">Weight</TableHead>
                        <TableHead className="text-xs h-10 text-right">Amount (₹)</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tripDetails.orders?.map((order) => (
                        <TableRow key={order.orderGroupId}>
                          <TableCell className="text-xs h-10 text-blue-500 cursor-pointer" onClick={() => { setSelectedOrderId(order.orderGroupId); setSelectedOrderDetails(order) }}>{order.orderGroupId}</TableCell>
                          <TableCell className="text-xs h-10">
                            <p>{order.buyerName}</p>
                            <p className="text-xs text-gray-500">{order.buyerLocality}</p>
                          </TableCell>
                          <TableCell className={`text-xs h-10 ${order.orderStatus === "Delivered" ? "text-primary" : order.orderStatus === "Cancelled" ? "text-destructive" : "text-yellow-700"}`}
                          >{order.orderStatus}</TableCell>
                          <TableCell className="text-xs h-10 whitespace-nowrap"><ShowDecimalAsSubscript value={formatCurrency(order.weight)} decFontSize="10px" /> kg</TableCell>
                          <TableCell className="text-xs h-10 text-right"><ShowDecimalAsSubscript value={formatCurrency(order.totalAmount)} decFontSize="10px" /></TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {tripDetails.returnItems?.length > 0 && (
                  <div className="space-y-2">
                    <h3 className="font-semibold">Return Items ({tripDetails.returnItems?.length})</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="text-xs h-10">Item</TableHead>
                          <TableHead className="text-xs h-10 text-center">Dispatched Qty (kg)</TableHead>
                          <TableHead className="text-xs h-10 text-center">Delivered Qty (kg)</TableHead>
                          <TableHead className="text-xs h-10 text-center">Returned Qty (kg)</TableHead>
                          <TableHead className="text-xs h-10 text-right">Returned Amount (₹)</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {tripDetails.returnItems?.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="text-xs h-10">{item.itemName}</TableCell>
                            <TableCell className="text-xs h-10 text-center">{item.dispatchedQty}</TableCell>
                            <TableCell className="text-xs h-10 text-center">{item.deliveredQty}</TableCell>
                            <TableCell className="text-xs h-10 text-center">{item.totalReturnedQty === 0 ? "-" : item.totalReturnedQty}</TableCell>
                            <TableCell className="text-xs h-10 text-right">{ item.totalReturnedAmount === 0 ? "-" : formatCurrency(item.totalReturnedAmount)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <User className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-lg font-medium text-gray-600">No trip found</p>
                    <p className="text-sm text-gray-500">No trip information available for this trip</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <TripOrderDetailsModal
        orderId={selectedOrderId}
        orderDetails={selectedOrderDetails}
        onClose={() => {
          setSelectedOrderId(null)
          setSelectedOrderDetails(null)
        }}
      />
    </div>
  )
}
