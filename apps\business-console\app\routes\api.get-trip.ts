
import { json, LoaderFunction } from "@remix-run/node";
import { getTrip } from "~/services/myTripsService";

export const loader: LoaderFunction = async ({ request }) => {
  const url = new URL(request.url);
  const tripId = Number(url.searchParams.get("tripId"));
  const role = url.searchParams.get("role") || undefined;

  if (!tripId) {
    return json({ error: "Trip Id is required" }, { status: 400 });
  }

  try {
    const trip = await getTrip(tripId, role, request);
    return json({ tripDetails: trip.data });
  } catch (error) {
    return json(
      { error: "Fetching trip failed" },
      { status: 500 }
    );
  }
};
