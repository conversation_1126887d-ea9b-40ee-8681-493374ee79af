import { API_BASE_URL, apiRequest } from "@utils/api";
import { Transaction } from "~/types/api/businessConsoleService/payments";
import { InitiatePayment, Payout, PayoutDetails, WalletHistory, WalletInfo } from "~/types/api/businessConsoleService/Payouts";
import { ApiResponse } from "~/types/api/Api";

export async function getTransactionDetails(
  date: string,
  request: Request
): Promise<ApiResponse<Transaction[]>> {
  try {
    const response = await apiRequest<Transaction[]>(
      `${API_BASE_URL}/mc/deposits/${date}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch buyer summary data");
    }
  } catch (err) {
    throw new Error("Failed to fetch buyer summary data");
  }
}

export async function updateTransaction(
  depositId: number | null,
  request: Request
): Promise<ApiResponse<Transaction>> {
  return apiRequest<Transaction>(
    `${API_BASE_URL}/mc/deposit/${depositId}/updatestatus`,
    "PUT", // Fixed capitalization for consistency
    undefined,
    {},
    true,
    request
  );
}

export async function updateMarkAsPaid(
  userId: number,
  depositId: number,
  request: Request
): Promise<ApiResponse<Transaction[]>> {
  return apiRequest<Transaction[]>(
    `${API_BASE_URL}/bc/user/${userId}/deposit/${depositId}/manualstatusupdate`,
    "PUT", // Fixed capitalization for consistency
    undefined,
    {},
    true,
    request
  );
}

export async function getWalletInfo(
  request: Request
): Promise<ApiResponse<WalletInfo>> {
  try {
    const response = await apiRequest<WalletInfo>(
      `${API_BASE_URL}/bc/seller/wallet`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch wallet information");
    }
  } catch (err) {
    throw new Error("Failed to fetch wallet information");
  }
}

export async function getPayoutList(
  pageNo: number,
  pageSize: number,
  request: Request
): Promise<ApiResponse<Payout[]>> {
  try {
    const response = await apiRequest<Payout[]>(
      `${API_BASE_URL}/bc/seller/payouts?pageNo=${pageNo}&size=${pageSize}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch payout list");
    }
  } catch (err) {
    throw new Error("Failed to fetch payout list");
  }
}

export async function getPayoutDetails(
  payoutId: number,
  request: Request
): Promise<ApiResponse<PayoutDetails>> {
  try {
    const response = await apiRequest<PayoutDetails>(
      `${API_BASE_URL}/bc/seller/payout/${payoutId}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch payout details");
    }
  } catch (err) {
    throw new Error("Failed to fetch payout details");
  }
}

export async function initiateWithdraw(
  userId: number,
  amount: number,
  request: Request
): Promise<ApiResponse<InitiatePayment>> {
  try {
    const response = await apiRequest<InitiatePayment>(
      `${API_BASE_URL}/wallet/${userId}/initiatepayment?amount=${amount}`,
      "POST",
      {},
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to initiate withdrawal");
    }
  } catch (err) {
    throw new Error("Failed to initiate withdrawal");
  }
}

export async function confirmWithdraw(
  userId: number,
  paymentInitiationId: number,
  request: Request
): Promise<ApiResponse<void>> {
  try {
    const response = await apiRequest<void>(
      `${API_BASE_URL}/wallet/${userId}/confirmpayment?initiationid=${paymentInitiationId}`,
      "POST",
      {},
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to confirm withdrawal");
    }
  } catch (err) {
    throw new Error("Failed to confirm withdrawal");
  }
}

export async function cancelWithdraw(
  userId: number,
  paymentInitiationId: number,
  request: Request
): Promise<ApiResponse<void>> {
  try {
    const response = await apiRequest<void>(
      `${API_BASE_URL}/wallet/${userId}/cancelpayment?initiationid=${paymentInitiationId}`,
      "PUT",
      {},
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to cancel withdrawal");
    }
  } catch (err) {
    throw new Error("Failed to cancel withdrawal");
  }
}

export async function getWalletHistory(
  request: Request,
  pageNo: number,
  pageSize: number,
  fromDate?: string,
  toDate?: string
): Promise<ApiResponse<{ walletEntries: WalletHistory[], totalElements: number, totalPages: number, currentPage: number, pageSize: number, hasNext: boolean, hasPrevious: boolean }>> {
  try {
    const url = `${API_BASE_URL}/bc/seller/wallet/history?pageNo=${pageNo}&pageSize=${pageSize}${fromDate ? `&fromDate=${fromDate}` : ''}${toDate ? `&toDate=${toDate}` : ''}`;
    const response = await apiRequest<{ walletEntries: WalletHistory[], totalElements: number, totalPages: number, currentPage: number, pageSize: number, hasNext: boolean, hasPrevious: boolean }>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch wallet history");
    }
  } catch (err) {
    throw new Error("Failed to fetch wallet history");
  }
}
