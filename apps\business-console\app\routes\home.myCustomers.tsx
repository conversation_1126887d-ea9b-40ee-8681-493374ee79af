import { LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { Truck, User } from "lucide-react";
import ShowDecimalAsSubscript from "~/components/common/ShowDecimalAsSubscript";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { CustomerCreditSummary, getCustomerCreditSummary } from "~/services/myCustomers";
import { withAuth, withResponse } from "~/utils/auth-utils";

interface LoaderData {
  customerCreditSummary: CustomerCreditSummary[];
  error?: string;
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  try {
    const response = await getCustomerCreditSummary(request);

    return withResponse({
      customerCreditSummary: response.data
    }, response.headers);
  } catch (error) {
    return withResponse({
      customerCreditSummary: [],
      error: "Failed to load customer credit summary"
    }, new Headers());
  }
});



export default function MyCustomers() {
  const { customerCreditSummary, error } = useLoaderData<LoaderData>();
  const navigate = useNavigate();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Calculate summary
  const totalCustomers = customerCreditSummary.length;
  const totalCreditPendingAmount = customerCreditSummary.reduce((sum, customer) => sum + (customer.totalCreditPendingAmount ?? 0), 0);

  const handleCustomerClick = (customer: CustomerCreditSummary) => {
    navigate(`/home/<USER>/${customer.nBuyerId}`, {
      state: { customerDetails: customer }
    });
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-7xl">
      <div className="space-y-4 sm:space-y-6">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">Customer Pending Payments</h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1">Manage your customers and track credit collections</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-white/80">
            <CardHeader className="flex flex-col flex-wrap sm:flex-row justify-between sm:items-end gap-2 sm:gap-4 p-4">
              <div>
                <CardTitle className="text-base md:text-lg text-gray-900">
                  Total Customers
                </CardTitle>
                <CardDescription className="text-xs md:text-sm text-gray-600">
                  Active customers working with you
                </CardDescription>
              </div>
              <div className="text-xl md:text-2xl font-bold text-gray-900">
                {totalCustomers}
              </div>
            </CardHeader>
          </Card>

          <Card className="bg-white/80">
            <CardHeader className="flex flex-col flex-wrap sm:flex-row justify-between sm:items-end gap-2 sm:gap-4 p-4">
              <div>
                <CardTitle className="text-base md:text-lg text-orange-600">
                  Credit Pending
                </CardTitle>
                <CardDescription className="text-xs md:text-sm text-gray-600">
                  Outstanding credit amount
                </CardDescription>
              </div>
              <div className="text-lg md:text-xl font-bold whitespace-nowrap">
                ₹ <ShowDecimalAsSubscript value={formatCurrency(totalCreditPendingAmount)} decFontSize="13px" />
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Customers List */}
        <Card className="border-0 shadow-md">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-t-lg p-2 sm:p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-100 rounded-lg flex-shrink-0">
                <User className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600" />
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Customer List</CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                  View all your customers and their credit information
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {customerCreditSummary && customerCreditSummary.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="font-semibold text-gray-900 whitespace-nowrap">Customer</TableHead>
                      <TableHead className="font-semibold text-gray-900">Area</TableHead>
                      <TableHead className="font-semibold text-gray-900 whitespace-nowrap text-center">Pending Orders</TableHead>
                      <TableHead className="font-semibold text-gray-900 whitespace-nowrap text-right">Credit Pending (₹)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {customerCreditSummary.sort((a, b) => sortCustomers(a, b)).map((customer, index) => (
                      <TableRow
                        key={customer.nBuyerId}
                        className="hover:bg-gray-50"
                        style={{
                          animationDelay: `${index * 50}ms`,
                        }}
                      >
                        <TableCell className="font-medium cursor-pointer text-blue-400" onClick={() => handleCustomerClick(customer)}>
                          {customer.buyerName}
                        </TableCell>
                        <TableCell className="text-sm">{customer.locality}</TableCell>
                        <TableCell className="text-sm text-center">{customer.PendingOrderCount}</TableCell>
                        <TableCell className="text-right font-medium">
                          {customer.totalCreditPendingAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(customer.totalCreditPendingAmount || 0)} decFontSize="10px" />}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <Truck className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-lg font-medium text-gray-600">No customers found</p>
                    <p className="text-sm text-gray-500">Your customers list will appear here once you start working with customers</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export function sortCustomers(a: CustomerCreditSummary, b: CustomerCreditSummary): number {
  const hasCreditA = (a.totalCreditPendingAmount ?? 0) > 0 ? 1 : 0;
  const hasCreditB = (b.totalCreditPendingAmount ?? 0) > 0 ? 1 : 0;

  // Items with credit pending should come first
  if (hasCreditA !== hasCreditB) {
    return hasCreditB - hasCreditA;
  }

  // Then sort alphabetically by buyerName
  return (a.buyerName ?? "").localeCompare(b.buyerName ?? "");
}