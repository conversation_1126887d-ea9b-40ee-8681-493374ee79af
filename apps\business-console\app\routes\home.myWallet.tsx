import { LoaderFunction, ActionFunction } from "@remix-run/node";
import { useLoaderData, useNavigate, useSearchParams, useActionData, Form, useNavigation } from "@remix-run/react";
import { useState, useEffect } from "react";
import { Wallet, CalendarIcon, History, Download, AlertCircle, CheckCircle, X } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Popover, PopoverContent, PopoverTrigger, PopoverClose } from "~/components/ui/popover";
import { Calendar } from "~/components/ui/calendar";
import { cn } from "~/lib/utils";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";
import dayjs from "dayjs";
import { WalletInfo, WalletHistory, InitiatePayment } from "~/types/api/businessConsoleService/Payouts";
import { getWalletInfo, getWalletHistory, initiateWithdraw, confirmWithdraw, cancelWithdraw } from "~/services/payments";
import { withAuth, withResponse } from "~/utils/auth-utils";
import ResponsivePagination from "~/components/ui/responsivePagination";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { AlertDialog, AlertDialogOverlay, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter } from "~/components/ui/alert-dialog";
import ShowDecimalAsSubscript from "~/components/common/ShowDecimalAsSubscript";

interface LoaderData {
  walletInfo: WalletInfo;
  walletHistory: {
    walletEntries: WalletHistory[];
    totalElements: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
  pageNo: number;
  pageSize: number;
  fromDate?: string;
  toDate?: string;
  error?: string;
}

interface ActionData {
  success?: boolean;
  error?: string;
  message?: string;
  paymentInitiationId?: number;
  initiatePaymentData?: InitiatePayment;
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  const url = new URL(request.url);
  const pageNo = Number(url.searchParams.get("pageNo")) || 0;
  const pageSize = Number(url.searchParams.get("pageSize")) || 100;
  const fromDate = url.searchParams.get("fromDate") || undefined;
  const toDate = url.searchParams.get("toDate") || undefined;

  try {
    const [walletResponse, historyResponse] = await Promise.all([
      getWalletInfo(request),
      getWalletHistory(request, pageNo, pageSize, fromDate, toDate)
    ]);

    return withResponse({
      walletInfo: walletResponse.data,
      walletHistory: historyResponse.data,
      pageNo,
      pageSize,
      fromDate,
      toDate
    }, walletResponse.headers);
  } catch (error) {
    console.error("My Wallet loader error:", error);

    return withResponse({
      walletInfo: null,
      walletHistory: {
        walletEntries: [],
        totalElements: 0,
        totalPages: 0,
        currentPage: 0,
        pageSize: 10,
        hasNext: false,
        hasPrevious: false
      },
      pageNo,
      pageSize,
      fromDate,
      toDate,
      error: "Failed to load wallet data"
    }, new Headers());
  }
});

export const action: ActionFunction = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const actionType = formData.get("_action");

  if (actionType === "initiate_withdraw") {
    try {
      const amount = Number(formData.get("amount"));
      if (!amount || amount <= 0) {
        return { success: false, error: "Invalid withdrawal amount" };
      }

      const response = await initiateWithdraw(user.userId, amount, request);
      return {
        success: true,
        message: "Withdrawal initiated.",
        paymentInitiationId: response.data?.paymentInitiationId,
        initiatePaymentData: response.data
      };
    } catch (error) {
      return { success: false, error: "Failed to initiate withdrawal" };
    }
  }

  if (actionType === "confirm_withdraw") {
    try {
      const paymentInitiationId = Number(formData.get("paymentInitiationId"));
      if (!paymentInitiationId) {
        return { success: false, error: "Invalid payment initiation ID" };
      }

      await confirmWithdraw(user.userId, paymentInitiationId, request);
      return {
        success: true,
        message: "Withdrawal confirmed successfully. Your funds will be transferred to your bank account."
      };
    } catch (error) {
      return { success: false, error: "Failed to confirm withdrawal" };
    }
  }

  if (actionType === "cancel_withdraw") {
    try {
      const paymentInitiationId = Number(formData.get("paymentInitiationId"));
      if (!paymentInitiationId) {
        return { success: false, error: "Invalid payment initiation ID" };
      }

      await cancelWithdraw(user.userId, paymentInitiationId, request);
      return {
        success: true,
        message: "Withdrawal cancelled successfully"
      };
    } catch (error) {
      return { success: false, error: "Failed to cancel withdrawal" };
    }
  }

  return { success: false, error: "Invalid action" };
});

export default function MyWallet() {
  const { walletInfo, walletHistory, pageNo, pageSize, fromDate, toDate, error } = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const isSubmitting = (navigation.state === "submitting" || navigation.state === "loading") && (navigation.formMethod === "POST" || navigation.formMethod === "PUT");
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Modal state management
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawalData, setWithdrawalData] = useState<InitiatePayment | null>(null);
  const [modalStep, setModalStep] = useState<'confirm' | 'processing' | 'success' | 'error'>('confirm');

  // Date filter state
  const [filterDate, setFilterDate] = useState<DateRange>({
    from: fromDate ? new Date(fromDate) : undefined,
    to: toDate ? new Date(toDate) : undefined,
  });
  const [dateRange, setDateRange] = useState<DateRange>({
    from: fromDate ? new Date(fromDate) : undefined,
    to: toDate ? new Date(toDate) : undefined,
  });

  // Handle action data changes
  useEffect(() => {
    if (actionData?.success && actionData?.initiatePaymentData) {
      // Withdrawal initiated successfully, show confirmation modal
      setWithdrawalData(actionData.initiatePaymentData);
      setShowWithdrawModal(true);
      setModalStep('confirm');
    } else if (actionData?.success && !actionData?.initiatePaymentData) {
      // Confirm or cancel action completed
      setModalStep('success');
      setTimeout(() => {
        setShowWithdrawModal(false);
        setWithdrawalData(null);
        setModalStep('confirm');
      }, 3000);
    } else if (actionData?.error) {
      setModalStep('error');
    }
  }, [actionData]);

  const handleWithdrawClick = () => {
    // This will trigger the form submission for initiate_withdraw
    const form = document.getElementById('withdraw-form') as HTMLFormElement;
    if (form) {
      form.requestSubmit();
    }
  };

  const handleConfirmWithdraw = () => {
    setModalStep('processing');
    const form = document.getElementById('confirm-form') as HTMLFormElement;
    if (form) {
      form.requestSubmit();
    }
  };

  const handleCancelWithdraw = () => {
    setModalStep('processing');
    const form = document.getElementById('cancel-form') as HTMLFormElement;
    if (form) {
      form.requestSubmit();
    }
  };

  const closeModal = () => {
    setShowWithdrawModal(false);
    setWithdrawalData(null);
    setModalStep('confirm');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("pageNo", newPage.toString());
    navigate(`?${params.toString()}`);
  };

  const handleDateFilter = () => {
    const params = new URLSearchParams(searchParams);

    if (dateRange.from) {
      const fromDateFormat = dayjs(dateRange.from).format('YYYY-MM-DD');
      params.set("fromDate", fromDateFormat);
    } else {
      params.delete("fromDate");
    }

    if (dateRange.to) {
      const toDateFormat = dayjs(dateRange.to).format('YYYY-MM-DD');
      params.set("toDate", toDateFormat);
    } else {
      if (dateRange.from) {
        const toDateFormat = dayjs(dateRange.from).format('YYYY-MM-DD');
        params.set("toDate", toDateFormat);
      } else {
        params.delete("toDate");
      }
    }

    setFilterDate(dateRange);
    params.set("pageNo", "0");
    navigate(`?${params.toString()}`);
  };

  const clearDateFilter = () => {
    const params = new URLSearchParams(searchParams);
    params.delete("fromDate");
    params.delete("toDate");
    params.set("pageNo", "0");
    setFilterDate({ from: undefined, to: undefined });
    setDateRange({ from: undefined, to: undefined });
    navigate(`?${params.toString()}`);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!walletInfo) {
    return <SpinnerLoader loading={true} />;
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-7xl">

      {/* Page Header */}
      <div>
        {/* <Button variant="secondary" size="sm" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button> */}
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">My Wallet</h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1">Manage your wallet and view wallet history</p>
        </div>
      </div>

      {actionData?.success && (
        actionData.message === "Withdrawal cancelled successfully" ? (
          <Alert className="border-red-200 bg-red-50">
            <CheckCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {actionData.message}
            </AlertDescription>
          </Alert>
        ) : (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              {actionData.message}
            </AlertDescription>
          </Alert>
        )
      )}

      {actionData?.error && (
        <Alert className="border-red-200 bg-red-50">
          <X className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {actionData.error}
          </AlertDescription>
        </Alert>
      )}

      {/* Wallet Information Card */}
      <Card className="border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg p-3 sm:p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                <Wallet className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Wallet Balance</CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                  Available funds in your wallet
                </CardDescription>
              </div>
            </div>
            <div className="text-left sm:text-right">
              <div className="text-2xl sm:text-3xl ml-1 font-bold text-gray-900 break-all">
                ₹ <ShowDecimalAsSubscript value={formatCurrency(walletInfo.walletBalance)} decFontSize="1rem" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-4 px-6">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <Button
              onClick={handleWithdrawClick}
              disabled={!walletInfo.withdrawEnabled || isSubmitting || walletInfo.walletBalance <= 0}
              className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-6 py-2 rounded-lg flex items-center justify-center space-x-2 text-sm sm:text-base"
            >
              <Download className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">{isSubmitting ? 'Processing...' : 'Withdraw Funds'}</span>
            </Button>
          </div>

          {/* Hidden forms for different actions */}
          <Form method="post" id="withdraw-form" style={{ display: 'none' }}>
            <input type="hidden" name="_action" value="initiate_withdraw" />
            <input type="hidden" name="amount" value={walletInfo.walletBalance} />
          </Form>

          <Form method="post" id="confirm-form" style={{ display: 'none' }}>
            <input type="hidden" name="_action" value="confirm_withdraw" />
            <input type="hidden" name="paymentInitiationId" value={withdrawalData?.paymentInitiationId || ''} />
          </Form>

          <Form method="post" id="cancel-form" style={{ display: 'none' }}>
            <input type="hidden" name="_action" value="cancel_withdraw" />
            <input type="hidden" name="paymentInitiationId" value={withdrawalData?.paymentInitiationId || ''} />
          </Form>
        </CardContent>
      </Card>

      {/* Wallet History Section */}
      <Card className="border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-t-lg p-2 sm:p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-100 rounded-lg flex-shrink-0">
                <History className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600" />
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Wallet History</CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                  View all your wallet transactions
                </CardDescription>
              </div>
            </div>

            {/* Date Filter */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full sm:w-auto justify-start text-left font-normal",
                      !filterDate.from && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filterDate?.from ? (
                      filterDate.to ? (
                        <>
                          {format(filterDate.from, "LLL dd, y")} - {format(filterDate.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(filterDate.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Pick a date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    initialFocus
                    selected={dateRange}
                    mode="range"
                    onSelect={(range: DateRange | undefined) => {
                      if (!range?.from) return;
                      setDateRange({
                        from: range.from,
                        to: range.to || undefined,
                      });
                    }}
                  />
                  <div className="flex gap-2 p-3">
                    <PopoverClose className="flex-1">
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => handleDateFilter()}
                      >
                        Apply Filter
                      </Button>
                    </PopoverClose>
                    <Button
                      variant="ghost"
                      className="flex-1"
                      onClick={clearDateFilter}
                    >
                      Clear
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              {(fromDate || toDate) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearDateFilter}
                  className="text-gray-500 hover:text-gray-700"
                >
                  Clear Filter
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {walletHistory?.walletEntries?.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="font-semibold text-gray-900">Date</TableHead>
                      <TableHead className="font-semibold text-gray-900 hidden sm:table-cell">Narration</TableHead>
                      <TableHead className="font-semibold text-gray-900 text-right whitespace-nowrap">Credit (₹)</TableHead>
                      <TableHead className="font-semibold text-gray-900 text-right whitespace-nowrap">Debit (₹)</TableHead>
                      <TableHead className="font-semibold text-gray-900 text-right whitespace-nowrap">Balance (₹)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {walletHistory?.walletEntries?.map((entry) => (
                      <TableRow key={entry.id} className="hover:bg-gray-50">
                        <TableCell className="font-medium whitespace-nowrap">
                          {formatDate(entry.transactionTime)}
                          <div className="mt-1 sm:hidden">{entry.narration || entry.note || '-'}</div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell" title={entry.narration}>
                          {entry.narration || entry.note || '-'}
                        </TableCell>
                        <TableCell className="text-right font-medium text-green-600 whitespace-nowrap">
                          {entry.creditValue === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(entry.creditValue)} decFontSize="10px" />}
                        </TableCell>
                        <TableCell className="text-right font-medium text-red-600 whitespace-nowrap">
                          {entry.debitValue === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(entry.debitValue)} decFontSize="10px" />}
                        </TableCell>
                        <TableCell className="text-right font-semibold whitespace-nowrap">
                          <ShowDecimalAsSubscript value={formatCurrency(entry.balance)} decFontSize="10px" />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <History className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {fromDate || toDate ? 'No transactions found for the selected date range.' : 'Your wallet history will appear here.'}
              </p>
            </div>
          )}

          {/* Pagination */}
          {walletHistory?.totalPages > 1 && (
            <div className="border-t bg-gray-50 px-4 py-3">
              <div>
                <ResponsivePagination
                  currentPage={pageNo}
                  totalPages={walletHistory.totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Withdrawal Confirmation Modal */}
      <AlertDialog open={showWithdrawModal} onOpenChange={closeModal}>
        <AlertDialogOverlay className="bg-black/10" />
        <AlertDialogContent className="sm:max-w-[500px]">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              {modalStep === 'confirm' && (
                <>
                  <AlertCircle className="h-5 w-5 text-blue-600" />
                  <span>Confirm Withdrawal</span>
                </>
              )}
              {modalStep === 'processing' && (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                  <span>Processing...</span>
                </>
              )}
              {modalStep === 'success' && (
                <>
                  <CheckCircle className={actionData?.message === "Withdrawal cancelled successfully" ? "h-5 w-5 text-red-600" : "h-5 w-5 text-green-600"} />
                  <span>Success</span>
                </>
              )}
              {modalStep === 'error' && (
                <>
                  <X className="h-5 w-5 text-red-600" />
                  <span>Error</span>
                </>
              )}
            </AlertDialogTitle>
          </AlertDialogHeader>

          {modalStep === 'confirm' && withdrawalData && (
            <>
              <AlertDialogDescription className="space-y-4">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-900 mb-2">Withdrawal Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Amount:</span>
                      <span className="font-semibold">₹ <ShowDecimalAsSubscript value={formatCurrency(walletInfo.walletBalance)} decFontSize="11px" /></span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Account Name:</span>
                      <span className="font-semibold">{withdrawalData.accountName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Account Number:</span>
                      <span className="font-semibold">{withdrawalData.accountNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">IFSC Code:</span>
                      <span className="font-semibold">{withdrawalData.ifscCode}</span>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  Please review the withdrawal details above. Once confirmed, the amount will be transferred to your registered bank account.
                </p>
              </AlertDialogDescription>
              <AlertDialogFooter className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="outline"
                  onClick={handleCancelWithdraw}
                  disabled={isSubmitting}
                  className="w-full sm:w-auto"
                >
                  Cancel Withdrawal
                </Button>
                <Button
                  onClick={handleConfirmWithdraw}
                  disabled={isSubmitting}
                  className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700"
                >
                  Confirm Withdrawal
                </Button>
              </AlertDialogFooter>
            </>
          )}

          {modalStep === 'processing' && (
            <AlertDialogDescription className="text-center py-8">
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <p className="text-gray-600">Processing your withdrawal request...</p>
              </div>
            </AlertDialogDescription>
          )}

          {modalStep === 'success' && (
            <AlertDialogDescription className="text-center py-8">
              <div className="flex flex-col items-center space-y-4">
                <CheckCircle className={actionData?.message === "Withdrawal cancelled successfully" ? "h-12 w-12 text-red-600" : "h-12 w-12 text-green-600"} />
                <div className="space-y-2">
                  <p className={actionData?.message === "Withdrawal cancelled successfully" ? "font-semibold text-red-800" : "font-semibold text-green-800"}>
                    {actionData?.message || 'Operation completed successfully!'}
                  </p>
                  <p className="text-sm text-gray-600">
                    This page will refresh automatically in a moment.
                  </p>
                </div>
              </div>
            </AlertDialogDescription>
          )}

          {modalStep === 'error' && (
            <>
              <AlertDialogDescription className="text-center py-8">
                <div className="flex flex-col items-center space-y-4">
                  <X className="h-12 w-12 text-red-600" />
                  <div className="space-y-2">
                    <p className="font-semibold text-red-800">
                      {actionData?.error || 'An error occurred'}
                    </p>
                    <p className="text-sm text-gray-600">
                      Please try again or contact support if the problem persists.
                    </p>
                  </div>
                </div>
              </AlertDialogDescription>
              <AlertDialogFooter>
                <Button onClick={closeModal} className="w-full">
                  Close
                </Button>
              </AlertDialogFooter>
            </>
          )}
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}