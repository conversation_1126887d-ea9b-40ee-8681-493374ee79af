import { useState } from "react";
import { json, type LoaderFunction, type ActionFunction } from "@remix-run/node";
import { useLoaderData, useSearchParams, useSubmit } from "@remix-run/react";
import { Input } from "@components/ui/input";
import { ScrollArea } from "@components/ui/scroll-area";
import { Search } from "lucide-react";
import { TemplateCard } from "@components/marketing/TemplateCard";
import { SendMessageDialog } from "@components/marketing/SendMessageDialog";
import { getTemplates, getCustomerGroups, sendMessage } from "../services/marketing";
import { searchParamsSchema } from "../schemas/marketing";
import type { Template, CustomerGroup } from "../schemas/marketing";
import { getSellerItems } from "../services/myItems";
import { getBuyerSummary } from "../services/businessConsoleService";
import type { SellerItem } from "../types/api/businessConsoleService/MyItemList";
import type { BuyerSummaryDetailsResponseItem } from "../types/api/businessConsoleService/BuyerSummaryDetailsResponseItem";
import { withAuth } from "~/utils/auth-utils";

export interface LoaderData {
  data: SellerItem[] | BuyerSummaryDetailsResponseItem[];
  templates: Template[];
  customerGroups: CustomerGroup[];
  statusCode: number;
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const searchParams = Object.fromEntries(url.searchParams);
  const validatedParams = searchParamsSchema.parse(searchParams);

  // Get search type from URL
  const searchType = url.searchParams.get("searchType");
  const search = url.searchParams.get("search") || "";
  
  let response;
  let templates;
  let customerGroups;
  
  // Handle different search types
  switch (searchType) {
    case "items":
      response = await getSellerItems(validatedParams.page - 1, validatedParams.pageSize, search, request);
      return json({ data: response.data, statusCode: 200 });
      
    case "customers":
      response = await getBuyerSummary(
        user.userId,
        validatedParams.page - 1,
        validatedParams.pageSize,
        "all",
        "name",
        search,
        "asc",
        undefined,
        request
      );
      return json({ data: response.data, statusCode: 200 });
      
    default:
      // Default case: load templates and customer groups
      templates = await getTemplates(validatedParams);
      customerGroups = await getCustomerGroups(validatedParams, request);
      return json({ 
        templates: templates.data, 
        customerGroups: customerGroups.data,
        statusCode: 200 
      });
  }
});

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const {id, templateId, variables, selectedGroup, selectedItems, selectedCustomers } = Object.fromEntries(formData);

  await sendMessage({
    id: Number(id),
    templateId: templateId as string,
    variables: JSON.parse(variables as string),
    selectedGroup: selectedGroup as string,
    selectedItems: selectedItems ? JSON.parse(selectedItems as string) : undefined,
    selectedCustomers: selectedCustomers ? JSON.parse(selectedCustomers as string) : undefined,
  }, request);

  return json({ success: true });
};

export default function Templates() {
  const { templates, customerGroups } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const submit = useSubmit();

  const handleSearch = (value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set("search", value);
    } else {
      params.delete("search");
    }
    params.set("page", "1"); // Reset to first page on search
    setSearchParams(params);
  };

  const handleSendMessage = (template: Template) => {
    setSelectedTemplate(template);
  };

  const handleSend = async (data: {
    id: number;
    templateId: string;
    variables: Record<string, string>;
    selectedGroup: string;
    selectedItems?: number[];
    selectedCustomers?: number[];
  }) => {
    const formData = new FormData();
    formData.set("id", data.id?.toString() || "");
    formData.set("templateId", data.templateId?.toString() || "");
    formData.set("variables", JSON.stringify(data.variables));
    formData.set("selectedGroup", data.selectedGroup);

    if (data.selectedItems) {
      formData.set("selectedItems", JSON.stringify(data.selectedItems));
    }
    if (data.selectedCustomers) {
      formData.set("selectedCustomers", JSON.stringify(data.selectedCustomers));
    }

    submit(formData, { method: "post" });
    setSelectedTemplate(null);
  };

  return (
    <div>
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search templates..."
          className="pl-10"
          value={searchParams.get("search") || ""}
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <ScrollArea className="h-[calc(100vh-100px)]">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templates.map((template: Template) => (
            <TemplateCard
              key={template.name}
              template={template}
              onSendMessage={handleSendMessage}
            />
          ))}
        </div>
      </ScrollArea>

      {selectedTemplate && (
        <SendMessageDialog
          template={selectedTemplate}
          showItems={selectedTemplate.variables.length > 0 && selectedTemplate.variables.includes("Item_Name")}
          showCustomers={true}
          showVariables={false}
          customerGroups={customerGroups}
          onClose={() => setSelectedTemplate(null)}
          onSend={handleSend}
        />
      )}
    </div>
  );
} 