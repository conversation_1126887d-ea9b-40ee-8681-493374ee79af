import { TripOrderDto } from "~/types/api/businessConsoleService/MyTrips";
import { useEffect, useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogOverlay } from "../ui/dialog"
import { Button } from "../ui/button"
import { Badge } from "../ui/badge"
import { Separator } from "../ui/separator"
import { Loader2, User, Package, DollarSign, MapPin, Clock, Phone, CreditCard } from "lucide-react"
import { format, toZonedTime } from "date-fns-tz";
import ShowDecimalAsSubscript from "../common/ShowDecimalAsSubscript";

/**
 * Trip Order Details Modal
 * @param orderId - Trip Order Id
 * @param orderDetails - Trip Order Details
 * @param onClose - Close Modal Callback
 * @returns
 */
export function TripOrderDetailsModal({
  orderId,
  orderDetails,
  onClose,
}: {
  orderId: number | null
  orderDetails: TripOrderDto | null
  onClose: () => void
}) {
  const [order, setOrder] = useState<TripOrderDto | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTripOrder = async (orderId: number) => {
    setLoading(true)
    try {
      const tripOrderResponse = await fetch(`/api/get-trip-order?orderId=${orderId}`)
      if (!tripOrderResponse.ok) throw new Error("Failed to fetch order details")
      const data = await tripOrderResponse.json()
      setOrder(data.tripOrderDetails)
      setError(null)
    } catch (err) {
      setOrder(null)
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (orderDetails && orderDetails.orderGroupId) {
      setOrder(orderDetails)
    } else if (orderId) {
      fetchTripOrder(orderId)
    }
  }, [orderId, orderDetails])

  return (
    <Dialog
      open={!!orderId}
      onOpenChange={(open) => {
        if (!open) {
          onClose()
        }
      }}
    >
      <DialogOverlay className="bg-black/10" />
      <DialogContent className="max-w-4xl rounded-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Order Details {order?.orderGroupId && `#${order.orderGroupId}`}
          </DialogTitle>
        </DialogHeader>

        <div className="max-h-[75vh] overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading order details...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-destructive">
              <p>Error loading order details: {error}</p>
            </div>
          ) : null}

          {order && !loading && (
            <div className="space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Buyer Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Name:&nbsp;</span> {order.buyerName}
                    </p>
                    <p>
                      <span className="font-medium">Primary Contact:&nbsp;</span> {order.primaryContactNumber}
                    </p>
                    {order.secondContactNumber ? (
                      <p>
                        <span className="font-medium">Secondary Contact:&nbsp;</span> {order.secondContactNumber}
                      </p>
                    ) : null}
                    <p>
                      <span className="font-medium">Locality:&nbsp;</span> {order.buyerLocality}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Delivery Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Service Area:&nbsp;</span> {order.serviceAreaName}
                    </p>
                    <p>
                      <span className="font-medium">Delivery Date:&nbsp;</span> {formatDate(order.deliveryDate)}
                    </p>
                    {order.deliveredTime ? (
                      <p>
                        <span className="font-medium">Delivered At:&nbsp;</span> {formatTime(order.deliveredTime)}
                      </p>
                    ) : null}
                    {order.deliveredBy ? (
                      <p>
                        <span className="font-medium">Delivered By:&nbsp;</span> {order.deliveredBy}
                      </p>
                    ) : null}
                    <p>
                      <span className="font-medium">Address:&nbsp;</span> {order.address}
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Order and Payment Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Order Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Item Count:&nbsp;</span> {order.itemCount}
                    </p>
                    <p>
                      <span className="font-medium">Weight:&nbsp;</span> <ShowDecimalAsSubscript value={formatCurrency(order.weight)} decFontSize="10px" /> kg
                    </p>
                    <p>
                      <span className="font-medium">Order Status:&nbsp;</span> 
                      <Badge className={`text-xs ${order.orderStatus === "Delivered" ? "bg-primary" : order.orderStatus === "Cancelled" ? "bg-destructive hover:bg-destructive/80" : "bg-yellow-700 hover:bg-yellow-700/80"}`}>
                        {order.orderStatus}
                      </Badge>
                    </p>
                    <p>
                      <span className="font-medium">Order Amount:&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(order.orderAmount)} decFontSize="10px" />
                    </p>
                    <p>
                      <span className="font-medium">Total Amount:&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(order.totalAmount)} decFontSize="10px" />
                    </p>
                    {order.deliveryCharge ? (
                      <p>
                        <span className="font-medium">Delivery Charge:&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(order.deliveryCharge)} decFontSize="10px" />
                      </p>
                    ) : null}
                    {order.discountAmount ? (
                      <p>
                        <span className="font-medium">Discount Amount:&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(order.discountAmount)} decFontSize="10px" />
                      </p>
                    ) : null}
                    {order.platformFee ? (
                      <p>
                        <span className="font-medium">Platform Fee:&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(order.platformFee)} decFontSize="10px" />
                      </p>
                    ) : null}
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Payment Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Cash Collection :&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(order.cashCollectionAmount)} decFontSize="10px" />
                    </p>
                    <p>
                      <span className="font-medium">Credit :&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(order.creditAmount)} decFontSize="10px" />
                    </p>
                    <p>
                      <span className="font-medium">Credit Allowed:&nbsp;</span> {order.creditAllowed ? "YES" : "NO"}
                    </p>
                    <p>
                      <span className="font-medium">Cash Allowed:&nbsp;</span> {order.cashAllowed ? "YES" : "NO"}
                    </p>
                    <p>
                      <span className="font-medium">Credit Pending:&nbsp;</span> ₹ <ShowDecimalAsSubscript value={formatCurrency(order.creditPendingAmount)} decFontSize="10px" />
                    </p>
                    {order.delayPayment ? (
                      <>
                        <p>
                          <span className="font-medium">Delay Payment Paid:&nbsp;</span> {order.delayPaymentPaid ? "YES" : "NO"}
                        </p>
                        <p>
                          <span className="font-medium">Delay Payment Paid Direct:&nbsp;</span> {order.delayPaymentPaidDirect ? "YES" : "NO"}
                        </p>
                      </>
                    ) : null}
                  </div>
                </div>
              </div>

              {/* Order Items */}
              {order.orderDetail && order.orderDetail.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    <h3 className="font-semibold">Order Items</h3>
                    <div className="space-y-2">
                      {order.orderDetail.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="flex-1">
                            <p className="font-medium text-sm">{item.itemName}</p>
                            <p className="text-xs text-muted-foreground">
                              {item.qty} {item.unit} × ₹ <ShowDecimalAsSubscript value={formatCurrency(item.price)} decFontSize="10px" />
                            </p>
                            {item.status && (
                              <Badge variant="outline" className="text-xs mt-1">
                                {item.status}
                              </Badge>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-sm"> ₹ <ShowDecimalAsSubscript value={formatCurrency(item.amount)} decFontSize="10px" /></p>
                            {item.cancelledQty > 0 && (
                              <p className="text-xs text-destructive">Cancelled: {item.cancelledQty}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}


function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

const formatTime = (dateString: string) => {
  const istTime = toZonedTime(dateString, "Asia/Kolkata");
  return format(istTime, "dd MMM, hh:mm a");
};

function formatCurrency(amount: number) {
  return new Intl.NumberFormat('en-IN', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}