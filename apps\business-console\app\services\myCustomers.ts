import { ApiResponse } from "~/types/api/Api";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export type CustomerCreditSummary = {
  nBuyerId: number,
  buyerName?: string,
  locality?: string,
  totalCreditPendingAmount?: number,
  PendingOrderCount?: number
}

export type CustomerPendingOrder = {
  orderGroupId: number,
  driverName: string,
  deliveryDate: string,
  creditPendingAmount: number
}

export async function getCustomerCreditSummary(request?: Request): Promise<ApiResponse<CustomerCreditSummary[]>> {
  try {
    const response = await apiRequest<CustomerCreditSummary[]>(
      `${API_BASE_URL}/bc/seller/customers/credit-pending`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch customer credit summary");
    }
  } catch (err) {
    throw new Error("Failed to fetch customer credit summary");
  }
}

export async function getCustomerPendingOrders(nBuyerId: number, request?: Request): Promise<ApiResponse<CustomerPendingOrder[]>> {
  try {
    const response = await apiRequest<CustomerPendingOrder[]>(
      `${API_BASE_URL}/bc/seller/customers/${nBuyerId}/pending-orders`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch customer pending orders");
    }
  } catch (err) {
    throw new Error("Failed to fetch customer pending orders");
  }
}

export async function recieveBuyerPendingCash(
  nBuyerId: number,
  amount: number,
  note?: string,
  request?: Request
): Promise<ApiResponse<void>> {
  try {
    const response = await apiRequest<void>(
      `${API_BASE_URL}/bc/seller/customers/${nBuyerId}/receive-cash`,
      "PUT",
      { amount, nBuyerId, note : note || "" },
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to recieve pending cash");
    }
  } catch (err) {
    throw new Error("Failed to recieve pending cash");
  }
}