import { LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Badge } from "~/components/ui/badge";
import { Truck, User } from "lucide-react";
import { DriverCreditSummary, getDriverCreditSummary } from "~/services/myDrivers";
import { withAuth, withResponse } from "~/utils/auth-utils";
import ShowDecimalAsSubscript from "~/components/common/ShowDecimalAsSubscript";

interface LoaderData {
  driverCreditSummary: DriverCreditSummary[];
  error?: string;
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  try {
    const response = await getDriverCreditSummary(request);

    return withResponse({
      driverCreditSummary: response.data
    }, response.headers);
  } catch (error) {
    return withResponse({
      driverCreditSummary: [],
      error: "Failed to load driver credit summary"
    }, new Headers());
  }
});

export default function MyDrivers() {
  const { driverCreditSummary, error } = useLoaderData<LoaderData>();
  const navigate = useNavigate();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Calculate summary
  const totalDrivers = driverCreditSummary.length;
  const totalReceivedAmount = driverCreditSummary.reduce((sum, driver) => sum + driver.totalReceivedAmount, 0);
  const totalCreditPendingAmount = driverCreditSummary.reduce((sum, driver) => sum + driver.totalCreditPendingAmount, 0);

  const handleDriverClick = (driver: DriverCreditSummary) => {
    navigate(`/home/<USER>/${driver.driverUserId}`, {
      state: { driverDetails: driver }
    });
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-7xl">
      <div className="space-y-4 sm:space-y-6">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">Driver Pending Payments</h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1">Manage your drivers and track credit collections</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-white/80">
            <CardHeader className="flex flex-col flex-wrap sm:flex-row justify-between sm:items-end gap-2 sm:gap-4 p-4">
              <div>
                <CardTitle className="text-base md:text-lg text-gray-900">
                  Total Drivers
                </CardTitle>
                <CardDescription className="text-xs md:text-sm text-gray-600">
                  Active drivers working with you
                </CardDescription>
              </div>
              <div className="text-xl md:text-2xl font-bold text-gray-900">
                {totalDrivers}
              </div>
            </CardHeader>
          </Card>

          <Card className="bg-white/80">
            <CardHeader className="flex flex-col flex-wrap sm:flex-row justify-between sm:items-end gap-2 sm:gap-4 p-4">
              <div>
                <CardTitle className="text-base md:text-lg text-emerald-600">
                  Total Collection
                </CardTitle>
                <CardDescription className="text-xs md:text-sm text-gray-600">
                  Amount collected by drivers
                </CardDescription>
              </div>
              <div className="text-lg md:text-xl font-bold whitespace-nowrap">
                ₹ <ShowDecimalAsSubscript value={formatCurrency(totalReceivedAmount)} decFontSize="13px" />
              </div>
            </CardHeader>
          </Card>

          <Card className="bg-white/80">
            <CardHeader className="flex flex-col flex-wrap sm:flex-row justify-between sm:items-end gap-2 sm:gap-4 p-4">
              <div>
                <CardTitle className="text-base md:text-lg text-orange-600">
                  Credit Pending
                </CardTitle>
                <CardDescription className="text-xs md:text-sm text-gray-600">
                  Outstanding credit amount
                </CardDescription>
              </div>
              <div className="text-lg md:text-xl font-bold whitespace-nowrap">
                ₹ <ShowDecimalAsSubscript value={formatCurrency(totalCreditPendingAmount)} decFontSize="13px" />
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Drivers List */}
        <Card className="border-0 shadow-md">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-t-lg p-2 sm:p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-100 rounded-lg flex-shrink-0">
                <User className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600" />
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Driver List</CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                  View all your drivers and their credit information
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {driverCreditSummary && driverCreditSummary.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="font-semibold text-gray-900 whitespace-nowrap">Driver</TableHead>
                      <TableHead className="font-semibold text-gray-900 whitespace-nowrap text-right">Collected (₹)</TableHead>
                      <TableHead className="font-semibold text-gray-900 whitespace-nowrap text-right">Pending (₹)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {driverCreditSummary.sort((a, b) => sortDrivers(a, b)).map((driver, index) => (
                      <TableRow
                        key={driver.driverUserId}
                        className="hover:bg-gray-50"
                        style={{
                          animationDelay: `${index * 50}ms`,
                        }}
                      >
                        <TableCell className="font-medium cursor-pointer text-blue-400" onClick={() => handleDriverClick(driver)}>
                          {driver.driverName}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {driver.totalReceivedAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(driver.totalReceivedAmount)} decFontSize="10px" />}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {driver.totalCreditPendingAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(driver.totalCreditPendingAmount)} decFontSize="10px" />}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <Truck className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-lg font-medium text-gray-600">No drivers found</p>
                    <p className="text-sm text-gray-500">Your drivers list will appear here once you start working with drivers</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function sortDrivers(a: DriverCreditSummary, b: DriverCreditSummary): number {
  const xa = a.totalCreditPendingAmount + a.totalReceivedAmount > 0 ? 1 : 0;
  const xb = b.totalCreditPendingAmount + b.totalReceivedAmount > 0 ? 1 : 0;

  if (xa !== xb) {
    return xb - xa; // active first
  }
  if (a.totalReceivedAmount === 0 || b.totalReceivedAmount === 0) {
    if (a.totalReceivedAmount !== b.totalReceivedAmount) {
      return b.totalReceivedAmount - a.totalReceivedAmount;
    }
  }
  return a.driverName?.localeCompare(b.driverName);
}