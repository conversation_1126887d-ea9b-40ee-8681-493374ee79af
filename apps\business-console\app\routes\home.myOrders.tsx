import { ActionFunction, json } from "@remix-run/node";
import { ShouldRevalidateFunction, useFetcher, useLoaderData } from "@remix-run/react";
import dayjs from "dayjs";
import { useCallback, useEffect, useState } from "react";
import { DateRange } from "react-day-picker";
import { useToast } from "~/hooks/use-toast";
import { useDebounceV1 } from "~/hooks/useDebounceV1";
import { getmNETAgents, getmNETDrivers, getmNETOrders } from "~/services/mNETOrders";
import { OrderAgent, OrderDriver, mNETOrder } from "~/types/api/businessConsoleService/mNETOrder";
import { OrderStatus } from "~/types/api/businessConsoleService/rNETOrder"
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Popover, PopoverContent, PopoverTrigger, PopoverClose } from "~/components/ui/popover";
import { Calendar } from "~/components/ui/calendar";
import { cn } from "~/lib/utils";
import { format } from "date-fns";
import {
  CalendarIcon,
  Package,
  User,
  Truck,
  RefreshCw,
  Download,
  CreditCard,
} from "lucide-react";
import ResponsivePagination from "~/components/ui/responsivePagination";
import { downloadExcelAsCSV } from "~/utils/excel";
import { Checkbox } from "~/components/ui/checkbox";
import ShowDecimalAsSubscript from "~/components/common/ShowDecimalAsSubscript";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc);
dayjs.extend(timezone);

function formatToIST(date: Date) {
  return dayjs(date).tz("Asia/Kolkata").format("YYYY-MM-DD");
}

interface LoaderData {
  agents: OrderAgent[];
  drivers: OrderDriver[];
}

export const loader = withAuth(async ({ request }) => {
  try {
    const agentsResponse = await getmNETAgents(request);
    const driversResponse = await getmNETDrivers(request);
    return withResponse({
      agents: agentsResponse.data,
      drivers: driversResponse.data
    }, agentsResponse.headers);
  } catch (error) {
    console.error("Failed to fetch agents and drivers");
    return withResponse({
      agents: [],
      drivers: []
    }, new Headers());
  }
});

export const shouldRevalidate: ShouldRevalidateFunction = ({ }) => {
  return false;
};

type ActionIntent = "Fetch Orders";

interface ActionData {
  intent: ActionIntent;
  errorMessage: string;
  success: boolean;
  data: { orders: mNETOrder[], totalElements: number, totalPages: number, pageSize: number, currentPage: number };
}

export const action: ActionFunction = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("intent") as ActionIntent;
  const data = formData.get("data") as any;

  if (!intent) {
    return json({ success: false, errorMessage: "Invalid request", intent: intent }, { status: 400 });
  }
  if (intent === "Fetch Orders") {
    const { codAmount, creditAmount, agentUserId, deliveredById, paymentStatus, status, fromDeliveryDate, toDeliveryDate, pageNo, pageSize } = JSON.parse(data);
    let queryParams = `?pageNo=${pageNo || 0}&pageSize=${pageSize || 50}`;
    if (codAmount === true) queryParams += `&codAmount=1`;
    if (creditAmount === true) queryParams += `&creditAmount=1`;
    if (agentUserId) queryParams += `&agentUserId=${agentUserId}`;
    if (deliveredById) queryParams += `&deliveredById=${deliveredById}`;
    if (paymentStatus !== null) queryParams += `&paymentStatus=${paymentStatus}`;
    if (status) queryParams += `&status=${status}`;
    if (fromDeliveryDate) queryParams += `&fromDeliveryDate=${formatToIST(fromDeliveryDate)}`
    if (toDeliveryDate) queryParams += `&toDeliveryDate=${formatToIST(toDeliveryDate)}`
    if (fromDeliveryDate && !toDeliveryDate) {
      queryParams += `&toDeliveryDate=${formatToIST(fromDeliveryDate)}`;
    }

    try {
      const orderResponse = await getmNETOrders(request, queryParams);
      return withResponse({ success: true, intent: intent, data: orderResponse?.data }, orderResponse?.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to fetch orders" }, { status: 400 })
    }
  }
  return json({ success: false, intent: intent, errorMessage: "Invalid intent" }, { status: 400 });
});

export default function MyOrders() {
  const { agents, drivers } = useLoaderData<LoaderData>();
  const [orders, setOrders] = useState<mNETOrder[]>([]);

  // action
  const fetcher = useFetcher<ActionData>();
  const { toast } = useToast();

  // search and filters
  const [searchCod, setSearchCod] = useState<boolean>(false)
  const [searchCredit, setSearchCredit] = useState<boolean>(false)
  const [selectedAgent, setSelectedAgent] = useState<OrderAgent | null>(null)
  const [selectedDriver, setSelectedDriver] = useState<OrderDriver | null>(null)
  const [filterPaymentStatus, setFilterPaymentStatus] = useState<boolean | null>(null);
  const [filterDate, setFilterDate] = useState<DateRange>({ from: undefined, to: undefined });  // date object
  const [filterStatus, setFilterStatus] = useState<OrderStatus | "Open" | "">("")
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [pageSize, setPageSize] = useState(50);
  const [currentPage, setCurrentPage] = useState(0);

  // local state
  const [dateRange, setDateRange] = useState<DateRange>({ from: undefined, to: undefined });  //date object

  // debounce on search term
  // const [debounceSearchCod, setDebounceSearchCod] = useDebounceV1(searchCod, 500);
  // const [debounceSearchCredit, setDebounceSearchCredit] = useDebounceV1(searchCredit, 500);

  const fetchOrders = useCallback(() => {
    console.log("Fetching orders...")

    const formData = new FormData();
    formData.append("intent", "Fetch Orders");
    const data = { codAmount: searchCod, creditAmount: searchCredit, agentUserId: selectedAgent?.agentUserId, deliveredById: selectedDriver?.driverUserId, paymentStatus: filterPaymentStatus, status: filterStatus, fromDeliveryDate: filterDate.from, toDeliveryDate: filterDate.to, pageNo: currentPage, pageSize: pageSize }
    formData.append("data", JSON.stringify(data))
    fetcher.submit(formData, { method: "post" })
  }, [searchCod, searchCredit, selectedAgent, selectedDriver, filterPaymentStatus, filterDate, filterStatus, pageSize, currentPage])

  useEffect(() => {
    fetchOrders()
  }, [searchCod, searchCredit, selectedAgent, selectedDriver, filterDate, filterPaymentStatus, filterStatus, pageSize, currentPage])

  useEffect(() => {
    // Handle successful/error responses
    if (fetcher.data?.intent === "Fetch Orders") {
      if (fetcher.data?.success) {
        fetcher.data?.data?.orders ? setOrders(fetcher.data.data.orders) : setOrders([])
        fetcher.data?.data?.totalElements ? setTotalElements(fetcher.data.data.totalElements) : setTotalElements(0)
        fetcher.data?.data?.totalPages ? setTotalPages(fetcher.data.data.totalPages) : setTotalPages(0)
        fetcher.data?.data?.pageSize ? setPageSize(fetcher.data.data.pageSize) : setPageSize(50)
        fetcher.data?.data?.currentPage ? setCurrentPage(fetcher.data.data.currentPage) : setCurrentPage(0)
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage || "Failed to fetch orders",
          variant: "destructive"
        })
      }
    }
  }, [fetcher.data])

  const clearFilters = () => {
    setSearchCod(false)
    setSearchCredit(false)
    setSelectedAgent(null)
    setSelectedDriver(null)
    setFilterPaymentStatus(null)
    setFilterDate({ from: undefined, to: undefined })
    setDateRange({ from: undefined, to: undefined })
    setFilterStatus("")
    setCurrentPage(0)
    setPageSize(50)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200 hover:bg-emerald-100'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200 hover:bg-red-100'
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-100'
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-100'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-100'
    }
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-7xl">
      <div className="space-y-4 sm:space-y-6">
        <div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">My Orders</h1>
            <p className="text-sm sm:text-base text-gray-600 mt-1">View and manage your orders</p>
          </div>
        </div>

        {/* Filters Section */}
        <Card className="bg-white/80">
          <CardContent className="p-3">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-700">COD Amount</Label>
                <div className="flex items-center space-x-2 p-2 rounded-md border border-gray-200 bg-white/70 hover:bg-white/90 transition-colors">
                  <Checkbox
                    id="searchCod"
                    checked={searchCod}
                    onCheckedChange={(checked) => setSearchCod(!!checked)}
                    className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                  />
                  <Label
                    htmlFor="searchCod"
                    className="text-sm text-gray-700 cursor-pointer select-none"
                  >
                    Filter by COD orders
                  </Label>
                </div>
              </div>

              <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-700">Credit Amount</Label>
                <div className="flex items-center space-x-2 p-2 rounded-md border border-gray-200 bg-white/70 hover:bg-white/90 transition-colors">
                  <Checkbox
                    id="searchCredit"
                    checked={searchCredit}
                    onCheckedChange={(checked) => setSearchCredit(!!checked)}
                    className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                  />
                  <Label
                    htmlFor="searchCredit"
                    className="text-sm text-gray-700 cursor-pointer select-none"
                  >
                    Filter by Credit orders
                  </Label>
                </div>
              </div>

              {/* Agent Select */}
              {/* <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-700">Agent</Label>
                <Select value={selectedAgent?.agentUserId.toString() || ""} onValueChange={(value) => {
                  const agent = agents.find(a => a.agentUserId.toString() === value);
                  setSelectedAgent(agent || null);
                }}>
                  <SelectTrigger className="bg-white/70 border-gray-200 focus:border-blue-500 h-9">
                    <User className="w-4 h-4 mr-2 text-gray-400" />
                    <SelectValue placeholder="Select agent" />
                  </SelectTrigger>
                  <SelectContent>
                    {agents.sort((a, b) => a.fullName.localeCompare(b.fullName)).map((agent) => (
                      <SelectItem key={agent.agentUserId} value={agent.agentUserId.toString()}>
                        {agent.fullName} - {agent.businessName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div> */}

              {/* Driver Select */}
              <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-700">Driver</Label>
                <Select value={selectedDriver?.driverUserId.toString() || ""} onValueChange={(value) => {
                  const driver = drivers.find(d => d.driverUserId.toString() === value);
                  setSelectedDriver(driver || null);
                }}>
                  <SelectTrigger className="bg-white/70 border-gray-200 focus:border-blue-500 h-9">
                    <Truck className="w-4 h-4 mr-2 text-gray-400" />
                    <SelectValue placeholder="Select driver" />
                  </SelectTrigger>
                  <SelectContent>
                    {drivers.sort((a, b) => a.fullName.localeCompare(b.fullName)).map((driver) => (
                      <SelectItem key={driver.driverUserId} value={driver.driverUserId.toString()}>
                        {driver.fullName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Payment Status */}
              <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-700">Payment Status</Label>
                <Select value={filterPaymentStatus === true ? "Completed" : filterPaymentStatus === false ? "Pending" : ""} onValueChange={(value: "Completed" | "Pending") => setFilterPaymentStatus(value === "Completed" ? true : value === "Pending" ? false : null)}>
                  <SelectTrigger className="bg-white/70 border-gray-200 focus:border-blue-500 h-9">
                    <CreditCard className="w-4 h-4 mr-2 text-gray-400" />
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Completed">Completed</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-700">Delivery Date Range</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal bg-white/70 border-gray-200 focus:border-blue-500 h-9",
                        !filterDate.from && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filterDate?.from ? (
                        filterDate.to ? (
                          <>
                            {format(filterDate.from, "LLL dd, y")} - {format(filterDate.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(filterDate.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Pick a date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      selected={dateRange}
                      mode="range"
                      onSelect={(range: DateRange | undefined) => {
                        if (!range?.from) return;
                        setDateRange({
                          from: range.from,
                          to: range.to || undefined,
                        });
                      }}
                    />
                    <PopoverClose className="w-full">
                      <Button
                        variant="ghost"
                        className="w-full text-blue-500 hover:text-blue-500 justify-center"
                        onClick={() => setFilterDate(dateRange)}
                      >
                        Apply Date Range
                      </Button>
                    </PopoverClose>
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-700">Order Status</Label>
                <Select value={filterStatus} onValueChange={(value: OrderStatus | "Open" | "") => setFilterStatus(value)}>
                  <SelectTrigger className="bg-white/70 border-gray-200 focus:border-blue-500 h-9">
                    <Package className="w-4 h-4 mr-2 text-gray-400" />
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Open">Open</SelectItem>
                    <SelectItem value="Dispatched">Dispatched</SelectItem>
                    <SelectItem value="Delivered">Delivered</SelectItem>
                    <SelectItem value="Cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1 flex flex-col justify-end">
                <Button
                  onClick={clearFilters}
                  variant="outline"
                  className="w-full bg-gradient-to-r from-red-50 to-pink-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-all duration-300 h-9"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders Table */}
        <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Package className="w-5 h-5 text-blue-600" />
                Orders ({totalElements} total)
              </CardTitle>
              {/* <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-white/70 border-gray-200 hover:bg-gray-50"
                  onClick={() => downloadExcelAsCSV(orders, "my_orders")}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div> */}
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100">
                    <TableHead className="font-semibold text-gray-700 whitespace-nowrap">Order ID</TableHead>
                    <TableHead className="font-semibold text-gray-700">Buyer</TableHead>
                    <TableHead className="font-semibold text-gray-700">Agent</TableHead>
                    <TableHead className="font-semibold text-gray-700">Driver</TableHead>
                    <TableHead className="font-semibold text-gray-700">Delivery Date</TableHead>
                    <TableHead className="font-semibold text-gray-700 whitespace-nowrap text-right">COD Amount (₹)</TableHead>
                    <TableHead className="font-semibold text-gray-700 whitespace-nowrap text-right">Credit Amount (₹)</TableHead>
                    <TableHead className="font-semibold text-gray-700 whitespace-nowrap text-right">Total Amount (₹)</TableHead>
                    <TableHead className="font-semibold text-gray-700 text-center">Status</TableHead>
                    <TableHead className="font-semibold text-gray-700 text-center">Payment</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-12">
                        <div className="flex flex-col items-center justify-center space-y-4">
                          <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                            <Package className="w-8 h-8 text-gray-400" />
                          </div>
                          <div>
                            <p className="text-lg font-medium text-gray-600">No orders found</p>
                            <p className="text-sm text-gray-500">Try adjusting your filters or search criteria</p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    orders.map((order, index) => (
                      <TableRow
                        key={order.id}
                        className="hover:bg-blue-50/50 transition-colors duration-200 group"
                        style={{
                          animationDelay: `${index * 50}ms`,
                        }}
                      >
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                            {order.id}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">{order.buyerName}</span>
                        </TableCell>
                        <TableCell className="whitespace-nowrap">
                          <span>{order.agentName || ""}</span>
                        </TableCell>
                        <TableCell className="whitespace-nowrap">
                          <span>{order.deliveredByName || ""}</span>
                        </TableCell>
                        <TableCell className="whitespace-nowrap">
                          <span>{dayjs(order.deliveryDate).format("MMM DD, YYYY")}</span>
                        </TableCell>
                        <TableCell className="whitespace-nowrap font-medium text-green-700 text-right">
                          {order.codAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(order.codAmount)} decFontSize="10px" />}
                        </TableCell>
                        <TableCell className="whitespace-nowrap font-medium text-blue-700 text-right">
                          {order.totalCreditAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(order.totalCreditAmount)} decFontSize="10px" />}
                        </TableCell>
                        <TableCell className="whitespace-nowrap font-bold text-purple-700 text-right">
                          {order.totalOrderGroupAmount === 0 ? "-" : <ShowDecimalAsSubscript value={formatCurrency(order.totalOrderGroupAmount)} decFontSize="10px" />}
                        </TableCell>
                        <TableCell align="center">
                          <Badge className={cn("font-medium", getStatusBadgeColor(order.status))}>
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell align="center">
                          {order.status === "Delivered" && (
                            <Badge variant="outline">
                              {order.paymentCompleted ? "Completed" : "Pending"}
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Pagination */}
        {(
          <Card className="mt-6 bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="text-sm text-gray-600">
                    Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, totalElements)} of {totalElements} orders
                  </div>
                  <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                    <SelectTrigger className="w-32 bg-white/70 border-gray-200">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="25">25 per page</SelectItem>
                      <SelectItem value="50">50 per page</SelectItem>
                      <SelectItem value="100">100 per page</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <ResponsivePagination
                  totalPages={totalPages}
                  currentPage={currentPage}
                  onPageChange={handlePageChange}
                />
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Loading Overlay */}
      {(fetcher.state === "loading" || fetcher.state === "submitting") && (
        <div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 pointer-events-none z-50 p-4"
        >
          <div className="p-6 text-center">
            <div className="w-20 h-20 border-4 border-transparent border-t-blue-400 rounded-full animate-spin flex items-center justify-center">
              <div className="w-16 h-16 border-4 border-transparent border-t-red-400 rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}